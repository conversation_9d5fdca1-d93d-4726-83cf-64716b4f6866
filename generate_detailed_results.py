#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成详细的预测结果并保存到markdown文件
"""

import pandas as pd
import numpy as np
import random
from collections import defaultdict
import math

def load_data():
    """加载数据"""
    df_stats = pd.read_csv('team_stats_for_prediction.csv', encoding='utf-8')
    df_schedule = pd.read_csv('matches_utf8.csv', encoding='utf-8')
    return df_stats, df_schedule

def calculate_team_strength(df_stats):
    """计算队伍实力评分 (0-100)"""
    strengths = {}
    
    for _, row in df_stats.iterrows():
        team = row['队伍名称']
        
        # 基础实力指标 (权重: 40%)
        points_score = (row['积分'] / 30) * 40  # 假设满分30分
        
        # 攻防平衡 (权重: 25%)
        goal_diff_score = min(max((row['净胜球'] + 20) / 40, 0), 1) * 25
        
        # 状态趋势 (权重: 20%)
        win_rate_score = row['胜率'] * 20
        
        # 稳定性指标 (权重: 15%)
        games_played = row['已赛场次']
        stability_score = min(games_played / 10, 1) * 15  # 比赛场次越多越稳定
        
        # 综合实力评分
        total_strength = points_score + goal_diff_score + win_rate_score + stability_score
        strengths[team] = min(max(total_strength, 0), 100)  # 限制在0-100之间
    
    return strengths

def calculate_match_probabilities(team_a, team_b, strength_a, strength_b, is_home_a=True):
    """计算两队对阵的胜平负概率"""
    
    # 主场优势 (+3分实力)
    home_advantage = 3 if is_home_a else -3
    strength_diff = strength_a - strength_b + home_advantage
    
    # 使用sigmoid函数计算胜率
    win_prob = 1 / (1 + math.exp(-strength_diff / 15))  # 15是调节参数
    
    # 平局概率与实力差距成反比
    draw_prob = 0.3 * math.exp(-(strength_diff ** 2) / 400)  # 实力越接近平局概率越高
    
    # 失败概率
    loss_prob = 1 - win_prob - draw_prob
    
    # 确保概率和为1
    total = win_prob + draw_prob + loss_prob
    return win_prob/total, draw_prob/total, loss_prob/total

def get_remaining_matches(df_schedule):
    """获取剩余比赛"""
    completed_rounds = ['第1轮', '第2轮', '第3轮', '第4轮', '第5轮', '第6轮', '第7轮', '第8轮', '第9轮']
    remaining = df_schedule[~df_schedule['Round（轮次）'].isin(completed_rounds)]
    
    matches = []
    for _, row in remaining.iterrows():
        home_team = row['HomeTeam（主队）'].replace('市', '队')
        away_team = row['AwayTeam（客队）'].replace('市', '队')
        matches.append((home_team, away_team))
    
    return matches

def simulate_season(df_stats, remaining_matches, team_strengths, num_simulations=10000):
    """蒙特卡洛模拟剩余赛季"""
    
    # 初始化当前积分
    current_points = {}
    current_goal_diff = {}
    current_goals = {}
    
    for _, row in df_stats.iterrows():
        team = row['队伍名称']
        current_points[team] = row['积分']
        current_goal_diff[team] = row['净胜球']
        current_goals[team] = row['进球数']
    
    # 存储所有模拟结果
    final_rankings = []
    
    print(f"🎲 开始 {num_simulations} 次蒙特卡洛模拟...")
    
    for sim in range(num_simulations):
        # 复制当前状态
        sim_points = current_points.copy()
        sim_goal_diff = current_goal_diff.copy()
        sim_goals = current_goals.copy()
        
        # 模拟剩余比赛
        for home_team, away_team in remaining_matches:
            if home_team in team_strengths and away_team in team_strengths:
                # 计算对阵概率
                win_prob, draw_prob, loss_prob = calculate_match_probabilities(
                    home_team, away_team, 
                    team_strengths[home_team], team_strengths[away_team], 
                    is_home_a=True
                )
                
                # 随机决定比赛结果
                rand = random.random()
                if rand < win_prob:
                    # 主队胜
                    sim_points[home_team] += 3
                    goal_diff = random.choice([1, 2, 3])  # 简单的进球差模拟
                    sim_goal_diff[home_team] += goal_diff
                    sim_goal_diff[away_team] -= goal_diff
                    sim_goals[home_team] += goal_diff + random.choice([0, 1])
                elif rand < win_prob + draw_prob:
                    # 平局
                    sim_points[home_team] += 1
                    sim_points[away_team] += 1
                    goals = random.choice([0, 1, 2])
                    sim_goals[home_team] += goals
                    sim_goals[away_team] += goals
                else:
                    # 客队胜
                    sim_points[away_team] += 3
                    goal_diff = random.choice([1, 2, 3])
                    sim_goal_diff[away_team] += goal_diff
                    sim_goal_diff[home_team] -= goal_diff
                    sim_goals[away_team] += goal_diff + random.choice([0, 1])
        
        # 计算最终排名
        teams_final = []
        for team in sim_points.keys():
            teams_final.append((team, sim_points[team], sim_goal_diff[team], sim_goals[team]))
        
        # 按积分、净胜球、进球数排序
        teams_final.sort(key=lambda x: (x[1], x[2], x[3]), reverse=True)
        
        # 记录排名
        ranking = [team[0] for team in teams_final]
        final_rankings.append(ranking)
        
        if (sim + 1) % 1000 == 0:
            print(f"  完成 {sim + 1} 次模拟...")
    
    return final_rankings

def analyze_results(final_rankings):
    """分析模拟结果"""
    num_teams = len(final_rankings[0])
    num_sims = len(final_rankings)
    
    # 统计每个位置的概率
    position_probs = defaultdict(lambda: defaultdict(int))
    
    for ranking in final_rankings:
        for pos, team in enumerate(ranking, 1):
            position_probs[team][pos] += 1
    
    # 转换为概率
    for team in position_probs:
        for pos in position_probs[team]:
            position_probs[team][pos] = position_probs[team][pos] / num_sims
    
    return position_probs

def generate_probability_table(position_probs):
    """生成详细的概率分布表"""
    teams = list(position_probs.keys())
    teams.sort(key=lambda x: sum(pos * prob for pos, prob in position_probs[x].items()))
    
    # 创建表格
    table_lines = []
    table_lines.append("| 队伍 | 1名 | 2名 | 3名 | 4名 | 5名 | 6名 | 7名 | 8名 | 9名 | 10名 | 11名 | 12名 | 13名 |")
    table_lines.append("|------|-----|-----|-----|-----|-----|-----|-----|-----|-----|------|------|------|------|")
    
    for team in teams:
        row = f"| {team} |"
        for pos in range(1, 14):
            prob = position_probs[team].get(pos, 0)
            if prob >= 0.001:
                row += f" {prob:.1%} |"
            else:
                row += " 0% |"
        table_lines.append(row)
    
    return "\n".join(table_lines)

def main():
    """主函数"""
    print("🚀 生成详细预测结果...")
    
    # 加载数据并运行模拟
    df_stats, df_schedule = load_data()
    team_strengths = calculate_team_strength(df_stats)
    remaining_matches = get_remaining_matches(df_schedule)
    final_rankings = simulate_season(df_stats, remaining_matches, team_strengths, num_simulations=10000)
    position_probs = analyze_results(final_rankings)
    
    # 生成概率分布表
    prob_table = generate_probability_table(position_probs)
    
    print("📊 概率分布表已生成")
    print(prob_table)
    
    # 保存到文件
    with open('detailed_probability_table.md', 'w', encoding='utf-8') as f:
        f.write("# 详细概率分布表\n\n")
        f.write("## 各队在每个排名位置的概率分布\n\n")
        f.write(prob_table)
        f.write("\n\n*基于10,000次蒙特卡洛模拟结果*")
    
    print("✅ 详细结果已保存到 detailed_probability_table.md")

if __name__ == "__main__":
    main()
