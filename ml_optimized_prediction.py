#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于机器学习的权重优化预测系统
使用所有对战数据训练最优权重，然后进行蒙特卡洛模拟
"""

import pandas as pd
import numpy as np
import random
import math
from collections import defaultdict
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载数据"""
    df_stats = pd.read_csv('team_stats_for_prediction.csv', encoding='utf-8')
    df_results = pd.read_csv('football_matches_utf8.csv', encoding='utf-8')
    df_schedule = pd.read_csv('matches_utf8.csv', encoding='utf-8')
    return df_stats, df_results, df_schedule

class ELOSystem:
    """ELO评分系统"""
    
    def __init__(self, initial_rating=1500, k_factor=32):
        self.ratings = {}
        self.initial_rating = initial_rating
        self.k_factor = k_factor
        self.rating_history = defaultdict(list)
    
    def initialize_teams(self, teams):
        for team in teams:
            self.ratings[team] = self.initial_rating
            self.rating_history[team].append(self.initial_rating)
    
    def expected_score(self, rating_a, rating_b):
        return 1 / (1 + 10**((rating_b - rating_a) / 400))
    
    def update_ratings(self, team_a, team_b, score_a, score_b):
        # 计算实际得分
        if score_a > score_b:
            actual_a, actual_b = 1.0, 0.0
        elif score_a < score_b:
            actual_a, actual_b = 0.0, 1.0
        else:
            actual_a, actual_b = 0.5, 0.5
        
        # 计算期望得分
        expected_a = self.expected_score(self.ratings[team_a], self.ratings[team_b])
        expected_b = 1 - expected_a
        
        # 更新评分
        self.ratings[team_a] += self.k_factor * (actual_a - expected_a)
        self.ratings[team_b] += self.k_factor * (actual_b - expected_b)
        
        # 记录历史
        self.rating_history[team_a].append(self.ratings[team_a])
        self.rating_history[team_b].append(self.ratings[team_b])
    
    def get_ratings(self):
        return self.ratings.copy()

def build_training_dataset(df_results, df_stats):
    """构建训练数据集"""
    print("🔧 构建机器学习训练数据集...")
    
    # 初始化ELO系统
    teams = set()
    matches = []
    
    for _, row in df_results.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']) and pd.notna(row['参赛队伍1进球数']):
            team1, team2 = row['参赛队伍1'], row['参赛队伍2']
            goals1, goals2 = int(row['参赛队伍1进球数']), int(row['参赛队伍2进球数'])
            round_name = row['轮次']
            
            teams.add(team1)
            teams.add(team2)
            matches.append((round_name, team1, team2, goals1, goals2))
    
    # 按轮次排序
    matches.sort(key=lambda x: x[0])
    
    # 初始化ELO和累积统计
    elo = ELOSystem()
    elo.initialize_teams(teams)
    
    # 累积统计
    team_cumulative_stats = {}
    for team in teams:
        team_cumulative_stats[team] = {
            'games': 0, 'wins': 0, 'draws': 0, 'losses': 0,
            'goals_for': 0, 'goals_against': 0, 'points': 0,
            'home_games': 0, 'home_wins': 0, 'away_games': 0, 'away_wins': 0,
            'recent_form': [], 'big_wins': 0, 'big_losses': 0
        }
    
    # 构建训练样本
    training_features = []
    training_labels = []
    
    for i, (round_name, team1, team2, goals1, goals2) in enumerate(matches):
        # 获取比赛前的状态作为特征
        features = extract_match_features(team1, team2, elo.ratings, team_cumulative_stats)
        
        # 标签：比赛结果 (0=客胜, 1=平局, 2=主胜)
        if goals1 > goals2:
            label = 2  # 主胜
        elif goals1 < goals2:
            label = 0  # 客胜
        else:
            label = 1  # 平局
        
        training_features.append(features)
        training_labels.append(label)
        
        # 更新ELO评分
        elo.update_ratings(team1, team2, goals1, goals2)
        
        # 更新累积统计
        update_cumulative_stats(team_cumulative_stats, team1, team2, goals1, goals2)
    
    print(f"  构建了 {len(training_features)} 个训练样本")
    return np.array(training_features), np.array(training_labels), elo.get_ratings()

def extract_match_features(team1, team2, elo_ratings, cumulative_stats):
    """提取比赛特征"""
    features = []
    
    # ELO评分特征
    elo1 = elo_ratings.get(team1, 1500)
    elo2 = elo_ratings.get(team2, 1500)
    features.extend([
        elo1, elo2, elo1 - elo2,  # ELO评分和差值
        elo1 / (elo1 + elo2)      # ELO相对强度
    ])
    
    # 累积统计特征
    stats1 = cumulative_stats[team1]
    stats2 = cumulative_stats[team2]
    
    # 基础统计
    games1 = max(stats1['games'], 1)
    games2 = max(stats2['games'], 1)
    
    features.extend([
        # 积分相关
        stats1['points'], stats2['points'], stats1['points'] - stats2['points'],
        stats1['points'] / (games1 * 3), stats2['points'] / (games2 * 3),  # 积分率
        
        # 攻防数据
        stats1['goals_for'], stats2['goals_for'],
        stats1['goals_against'], stats2['goals_against'],
        (stats1['goals_for'] - stats1['goals_against']),  # 净胜球
        (stats2['goals_for'] - stats2['goals_against']),
        
        # 胜率数据
        stats1['wins'] / games1, stats2['wins'] / games2,  # 胜率
        stats1['draws'] / games1, stats2['draws'] / games2,  # 平局率
        
        # 主客场表现
        stats1['home_wins'] / max(stats1['home_games'], 1),  # 主场胜率
        stats2['away_wins'] / max(stats2['away_games'], 1),  # 客场胜率
        
        # 特殊指标
        stats1['big_wins'], stats2['big_wins'],  # 大胜场次
        stats1['big_losses'], stats2['big_losses'],  # 大败场次
    ])
    
    # 最近状态 (最近3场)
    recent1 = stats1['recent_form'][-3:] if len(stats1['recent_form']) >= 3 else stats1['recent_form']
    recent2 = stats2['recent_form'][-3:] if len(stats2['recent_form']) >= 3 else stats2['recent_form']
    
    features.extend([
        sum(recent1) / max(len(recent1), 1),  # 最近场均积分
        sum(recent2) / max(len(recent2), 1),
    ])
    
    # 主场优势 (固定特征)
    features.append(1)  # 主场标识
    
    return features

def update_cumulative_stats(cumulative_stats, team1, team2, goals1, goals2):
    """更新累积统计"""
    # 更新team1 (主队)
    stats1 = cumulative_stats[team1]
    stats1['games'] += 1
    stats1['home_games'] += 1
    stats1['goals_for'] += goals1
    stats1['goals_against'] += goals2
    
    if goals1 > goals2:
        stats1['wins'] += 1
        stats1['home_wins'] += 1
        stats1['points'] += 3
        stats1['recent_form'].append(3)
        if goals1 - goals2 >= 2:
            stats1['big_wins'] += 1
    elif goals1 == goals2:
        stats1['draws'] += 1
        stats1['points'] += 1
        stats1['recent_form'].append(1)
    else:
        stats1['losses'] += 1
        stats1['recent_form'].append(0)
        if goals2 - goals1 >= 2:
            stats1['big_losses'] += 1
    
    # 更新team2 (客队)
    stats2 = cumulative_stats[team2]
    stats2['games'] += 1
    stats2['away_games'] += 1
    stats2['goals_for'] += goals2
    stats2['goals_against'] += goals1
    
    if goals2 > goals1:
        stats2['wins'] += 1
        stats2['away_wins'] += 1
        stats2['points'] += 3
        stats2['recent_form'].append(3)
        if goals2 - goals1 >= 2:
            stats2['big_wins'] += 1
    elif goals2 == goals1:
        stats2['draws'] += 1
        stats2['points'] += 1
        stats2['recent_form'].append(1)
    else:
        stats2['losses'] += 1
        stats2['recent_form'].append(0)
        if goals1 - goals2 >= 2:
            stats2['big_losses'] += 1

def train_prediction_model(X, y):
    """训练预测模型"""
    print("🤖 训练机器学习模型...")
    
    # 标准化特征
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 尝试多种模型
    models = {
        'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42)
    }
    
    best_model = None
    best_score = 0
    best_name = ""
    
    for name, model in models.items():
        # 交叉验证
        scores = cross_val_score(model, X_scaled, y, cv=5, scoring='accuracy')
        avg_score = scores.mean()
        
        print(f"  {name}: {avg_score:.3f} ± {scores.std():.3f}")
        
        if avg_score > best_score:
            best_score = avg_score
            best_model = model
            best_name = name
    
    # 训练最佳模型
    best_model.fit(X_scaled, y)
    
    print(f"  最佳模型: {best_name} (准确率: {best_score:.3f})")
    
    # 特征重要性分析
    if hasattr(best_model, 'feature_importances_'):
        feature_names = [
            'ELO_team1', 'ELO_team2', 'ELO_diff', 'ELO_ratio',
            'Points_1', 'Points_2', 'Points_diff', 'Point_rate_1', 'Point_rate_2',
            'Goals_for_1', 'Goals_for_2', 'Goals_against_1', 'Goals_against_2',
            'Goal_diff_1', 'Goal_diff_2', 'Win_rate_1', 'Win_rate_2',
            'Draw_rate_1', 'Draw_rate_2', 'Home_win_rate', 'Away_win_rate',
            'Big_wins_1', 'Big_wins_2', 'Big_losses_1', 'Big_losses_2',
            'Recent_form_1', 'Recent_form_2', 'Home_advantage'
        ]
        
        importances = best_model.feature_importances_
        feature_importance = list(zip(feature_names, importances))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        print("\n  📊 特征重要性 TOP 10:")
        for i, (feature, importance) in enumerate(feature_importance[:10], 1):
            print(f"    {i:2d}. {feature:15s}: {importance:.3f}")
    
    return best_model, scaler

def predict_match_probabilities(model, scaler, team1, team2, current_elo, current_stats):
    """使用训练好的模型预测比赛概率"""
    # 提取特征
    features = extract_match_features(team1, team2, current_elo, current_stats)
    features_scaled = scaler.transform([features])
    
    # 预测概率
    probabilities = model.predict_proba(features_scaled)[0]
    
    # 返回 [客胜概率, 平局概率, 主胜概率]
    return probabilities[0], probabilities[1], probabilities[2]

def simulate_season_with_ml(df_stats, df_schedule, model, scaler, current_elo, num_simulations=5000):
    """使用机器学习模型进行蒙特卡洛模拟"""
    print(f"\n🎲 使用ML模型进行 {num_simulations} 次蒙特卡洛模拟...")
    
    # 获取剩余比赛
    completed_rounds = ['第1轮', '第2轮', '第3轮', '第4轮', '第5轮', '第6轮', '第7轮', '第8轮', '第9轮']
    remaining = df_schedule[~df_schedule['Round（轮次）'].isin(completed_rounds)]
    
    remaining_matches = []
    for _, row in remaining.iterrows():
        home_team = row['HomeTeam（主队）'].replace('市', '队')
        away_team = row['AwayTeam（客队）'].replace('市', '队')
        if home_team in current_elo and away_team in current_elo:
            remaining_matches.append((home_team, away_team))
    
    # 构建当前统计状态
    current_stats = {}
    for _, row in df_stats.iterrows():
        team = row['队伍名称']
        current_stats[team] = {
            'games': row['已赛场次'], 'wins': row['胜场'], 'draws': row['平场'], 'losses': row['负场'],
            'goals_for': row['进球数'], 'goals_against': row['失球数'], 'points': row['积分'],
            'home_games': row['主场胜场'] + row['主场平场'] + row['主场负场'],
            'home_wins': row['主场胜场'], 'away_games': row['客场胜场'] + row['客场平场'] + row['客场负场'],
            'away_wins': row['客场胜场'], 'recent_form': [1] * row['最近5场积分'],  # 简化
            'big_wins': row['大胜场次'], 'big_losses': row['大败场次']
        }
    
    # 初始化当前积分
    current_points = {row['队伍名称']: row['积分'] for _, row in df_stats.iterrows()}
    current_goal_diff = {row['队伍名称']: row['净胜球'] for _, row in df_stats.iterrows()}
    current_goals = {row['队伍名称']: row['进球数'] for _, row in df_stats.iterrows()}
    
    final_rankings = []
    
    for sim in range(num_simulations):
        sim_points = current_points.copy()
        sim_goal_diff = current_goal_diff.copy()
        sim_goals = current_goals.copy()
        
        # 模拟剩余比赛
        for home_team, away_team in remaining_matches:
            # 使用ML模型预测概率
            away_win_prob, draw_prob, home_win_prob = predict_match_probabilities(
                model, scaler, home_team, away_team, current_elo, current_stats
            )
            
            # 随机决定比赛结果
            rand = random.random()
            if rand < home_win_prob:
                # 主队胜
                sim_points[home_team] += 3
                goal_diff = random.choice([1, 2, 3])
                sim_goal_diff[home_team] += goal_diff
                sim_goal_diff[away_team] -= goal_diff
                sim_goals[home_team] += goal_diff + random.choice([0, 1])
            elif rand < home_win_prob + draw_prob:
                # 平局
                sim_points[home_team] += 1
                sim_points[away_team] += 1
                goals = random.choice([0, 1, 2])
                sim_goals[home_team] += goals
                sim_goals[away_team] += goals
            else:
                # 客队胜
                sim_points[away_team] += 3
                goal_diff = random.choice([1, 2, 3])
                sim_goal_diff[away_team] += goal_diff
                sim_goal_diff[home_team] -= goal_diff
                sim_goals[away_team] += goal_diff + random.choice([0, 1])
        
        # 计算最终排名
        teams_final = []
        for team in sim_points.keys():
            teams_final.append((team, sim_points[team], sim_goal_diff[team], sim_goals[team]))
        
        teams_final.sort(key=lambda x: (x[1], x[2], x[3]), reverse=True)
        ranking = [team[0] for team in teams_final]
        final_rankings.append(ranking)
        
        if (sim + 1) % 1000 == 0:
            print(f"  完成 {sim + 1} 次模拟...")
    
    return final_rankings

def analyze_ml_results(final_rankings):
    """分析ML预测结果"""
    position_probs = defaultdict(lambda: defaultdict(int))
    
    for ranking in final_rankings:
        for pos, team in enumerate(ranking, 1):
            position_probs[team][pos] += 1
    
    # 转换为概率
    num_sims = len(final_rankings)
    for team in position_probs:
        for pos in position_probs[team]:
            position_probs[team][pos] = position_probs[team][pos] / num_sims
    
    return position_probs

def print_ml_results(position_probs):
    """打印ML预测结果"""
    print("\n" + "="*60)
    print("🤖 基于机器学习的最终排名预测")
    print("="*60)
    
    # 计算期望排名
    expected_rankings = []
    for team in position_probs:
        expected_rank = sum(pos * prob for pos, prob in position_probs[team].items())
        expected_rankings.append((team, expected_rank))
    
    expected_rankings.sort(key=lambda x: x[1])
    
    print("\n📊 ML预测最终排名:")
    print("排名  队伍      期望排名  前三概率  保级概率")
    print("-" * 45)
    
    for i, (team, exp_rank) in enumerate(expected_rankings, 1):
        top3_prob = sum(position_probs[team].get(pos, 0) for pos in [1, 2, 3])
        safe_prob = sum(position_probs[team].get(pos, 0) for pos in range(1, 11))
        
        print(f"{i:2d}   {team:8s}  {exp_rank:6.1f}    {top3_prob:6.1%}    {safe_prob:6.1%}")

def main():
    """主函数"""
    print("🚀 基于机器学习的权重优化预测系统")
    print("特点: 用所有对战数据训练最优权重")
    print("="*50)
    
    # 加载数据
    df_stats, df_results, df_schedule = load_data()
    
    # 构建训练数据集
    X, y, current_elo = build_training_dataset(df_results, df_stats)
    
    # 训练模型
    model, scaler = train_prediction_model(X, y)
    
    # 蒙特卡洛模拟
    final_rankings = simulate_season_with_ml(df_stats, df_schedule, model, scaler, current_elo)
    
    # 分析结果
    position_probs = analyze_ml_results(final_rankings)
    
    # 打印结果
    print_ml_results(position_probs)
    
    print("\n💡 ML方法的优势:")
    print("✅ 数据驱动的权重优化")
    print("✅ 考虑所有可用特征")
    print("✅ 基于实际比赛结果训练")
    print("✅ 自动发现特征重要性")

if __name__ == "__main__":
    main()
