#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于XGBoost的足球比赛预测模型
将55场比赛数据一视同仁，预测主场队伍的胜/平/负
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

class ELOSystem:
    """ELO评分系统"""
    
    def __init__(self, initial_rating=1500, k_factor=32):
        self.ratings = {}
        self.initial_rating = initial_rating
        self.k_factor = k_factor
        self.rating_history = {}
    
    def initialize_teams(self, teams):
        for team in teams:
            self.ratings[team] = self.initial_rating
            self.rating_history[team] = [self.initial_rating]
    
    def expected_score(self, rating_a, rating_b):
        return 1 / (1 + 10**((rating_b - rating_a) / 400))
    
    def update_ratings(self, team_a, team_b, score_a, score_b):
        # 计算实际得分
        if score_a > score_b:
            actual_a, actual_b = 1.0, 0.0
        elif score_a < score_b:
            actual_a, actual_b = 0.0, 1.0
        else:
            actual_a, actual_b = 0.5, 0.5
        
        # 计算期望得分
        expected_a = self.expected_score(self.ratings[team_a], self.ratings[team_b])
        expected_b = 1 - expected_a
        
        # 更新评分
        old_rating_a = self.ratings[team_a]
        old_rating_b = self.ratings[team_b]
        
        self.ratings[team_a] += self.k_factor * (actual_a - expected_a)
        self.ratings[team_b] += self.k_factor * (actual_b - expected_b)
        
        # 记录历史
        self.rating_history[team_a].append(self.ratings[team_a])
        self.rating_history[team_b].append(self.ratings[team_b])
        
        return old_rating_a, old_rating_b
    
    def get_ratings(self):
        return self.ratings.copy()

def load_and_prepare_data():
    """加载并准备数据"""
    print("📊 加载比赛数据...")
    
    # 读取比赛结果
    df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
    
    print(f"原始数据: {len(df_results)} 条记录")
    
    # 清理数据
    valid_matches = []
    for _, row in df_results.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
            goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
            goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
            
            if pd.notna(row[goals1_col]) and pd.notna(row[goals2_col]):
                valid_matches.append({
                    'round': row['轮次'],
                    'home_team': row['参赛队伍1'],
                    'away_team': row['参赛队伍2'],
                    'home_goals': int(row[goals1_col]),
                    'away_goals': int(row[goals2_col])
                })
    
    print(f"有效比赛数据: {len(valid_matches)} 场")
    return valid_matches

def build_cumulative_stats(matches):
    """构建累积统计数据"""
    print("🔢 构建累积统计数据...")
    
    # 获取所有队伍
    teams = set()
    for match in matches:
        teams.add(match['home_team'])
        teams.add(match['away_team'])
    
    teams = sorted(list(teams))
    print(f"队伍数量: {len(teams)}")
    
    # 初始化ELO系统
    elo = ELOSystem()
    elo.initialize_teams(teams)
    
    # 初始化累积统计
    cumulative_stats = {}
    for team in teams:
        cumulative_stats[team] = {
            'games': 0, 'wins': 0, 'draws': 0, 'losses': 0,
            'goals_for': 0, 'goals_against': 0, 'points': 0,
            'home_games': 0, 'home_wins': 0, 'home_draws': 0, 'home_losses': 0,
            'away_games': 0, 'away_wins': 0, 'away_draws': 0, 'away_losses': 0,
            'recent_form': [], 'big_wins': 0, 'big_losses': 0,
            'clean_sheets': 0, 'failed_to_score': 0
        }
    
    # 按轮次排序比赛
    matches_sorted = sorted(matches, key=lambda x: x['round'])
    
    # 为每场比赛构建特征
    match_features = []
    
    for i, match in enumerate(matches_sorted):
        home_team = match['home_team']
        away_team = match['away_team']
        home_goals = match['home_goals']
        away_goals = match['away_goals']
        
        # 获取比赛前的ELO评分和统计数据
        home_elo = elo.ratings[home_team]
        away_elo = elo.ratings[away_team]
        
        # 提取特征
        features = extract_match_features(home_team, away_team, home_elo, away_elo, cumulative_stats)
        
        # 确定标签 (主场队伍视角)
        if home_goals > away_goals:
            label = 2  # 主胜
        elif home_goals < away_goals:
            label = 0  # 主负
        else:
            label = 1  # 平局
        
        match_features.append({
            'features': features,
            'label': label,
            'home_team': home_team,
            'away_team': away_team,
            'home_goals': home_goals,
            'away_goals': away_goals,
            'round': match['round']
        })
        
        # 更新ELO评分
        elo.update_ratings(home_team, away_team, home_goals, away_goals)
        
        # 更新累积统计
        update_cumulative_stats(cumulative_stats, home_team, away_team, home_goals, away_goals)
    
    return match_features, elo.get_ratings()

def extract_match_features(home_team, away_team, home_elo, away_elo, cumulative_stats):
    """提取比赛特征"""
    features = []
    
    # ELO评分特征
    features.extend([
        home_elo,                    # 主队ELO
        away_elo,                    # 客队ELO
        home_elo - away_elo,         # ELO差值
        home_elo / (home_elo + away_elo)  # ELO相对强度
    ])
    
    # 获取累积统计
    home_stats = cumulative_stats[home_team]
    away_stats = cumulative_stats[away_team]
    
    # 基础统计特征
    home_games = max(home_stats['games'], 1)
    away_games = max(away_stats['games'], 1)
    
    features.extend([
        # 积分相关
        home_stats['points'],
        away_stats['points'],
        home_stats['points'] - away_stats['points'],
        home_stats['points'] / (home_games * 3),  # 主队积分率
        away_stats['points'] / (away_games * 3),  # 客队积分率
        
        # 进球相关
        home_stats['goals_for'],
        away_stats['goals_for'],
        home_stats['goals_against'],
        away_stats['goals_against'],
        home_stats['goals_for'] - home_stats['goals_against'],  # 主队净胜球
        away_stats['goals_for'] - away_stats['goals_against'],  # 客队净胜球
        home_stats['goals_for'] / home_games,     # 主队场均进球
        away_stats['goals_for'] / away_games,     # 客队场均进球
        home_stats['goals_against'] / home_games, # 主队场均失球
        away_stats['goals_against'] / away_games, # 客队场均失球
        
        # 胜率相关
        home_stats['wins'] / home_games,          # 主队胜率
        away_stats['wins'] / away_games,          # 客队胜率
        home_stats['draws'] / home_games,         # 主队平局率
        away_stats['draws'] / away_games,         # 客队平局率
        home_stats['losses'] / home_games,        # 主队负率
        away_stats['losses'] / away_games,        # 客队负率
    ])
    
    # 主客场表现
    home_home_games = max(home_stats['home_games'], 1)
    away_away_games = max(away_stats['away_games'], 1)
    
    features.extend([
        home_stats['home_wins'] / home_home_games,    # 主队主场胜率
        away_stats['away_wins'] / away_away_games,    # 客队客场胜率
        home_stats['home_draws'] / home_home_games,   # 主队主场平局率
        away_stats['away_draws'] / away_away_games,   # 客队客场平局率
        home_stats['home_losses'] / home_home_games,  # 主队主场负率
        away_stats['away_losses'] / away_away_games,  # 客队客场负率
    ])
    
    # 特殊指标
    features.extend([
        home_stats['big_wins'],           # 主队大胜次数
        away_stats['big_wins'],           # 客队大胜次数
        home_stats['big_losses'],         # 主队大败次数
        away_stats['big_losses'],         # 客队大败次数
        home_stats['clean_sheets'],       # 主队零封次数
        away_stats['clean_sheets'],       # 客队零封次数
        home_stats['failed_to_score'],    # 主队零进球次数
        away_stats['failed_to_score'],    # 客队零进球次数
    ])
    
    # 最近状态 (最近3场)
    home_recent = home_stats['recent_form'][-3:] if len(home_stats['recent_form']) >= 3 else home_stats['recent_form']
    away_recent = away_stats['recent_form'][-3:] if len(away_stats['recent_form']) >= 3 else away_stats['recent_form']
    
    features.extend([
        sum(home_recent) / max(len(home_recent), 1),  # 主队最近场均积分
        sum(away_recent) / max(len(away_recent), 1),  # 客队最近场均积分
    ])
    
    return features

def update_cumulative_stats(cumulative_stats, home_team, away_team, home_goals, away_goals):
    """更新累积统计"""
    # 更新主队统计
    home_stats = cumulative_stats[home_team]
    home_stats['games'] += 1
    home_stats['home_games'] += 1
    home_stats['goals_for'] += home_goals
    home_stats['goals_against'] += away_goals
    
    if home_goals > away_goals:
        home_stats['wins'] += 1
        home_stats['home_wins'] += 1
        home_stats['points'] += 3
        home_stats['recent_form'].append(3)
        if home_goals - away_goals >= 2:
            home_stats['big_wins'] += 1
    elif home_goals == away_goals:
        home_stats['draws'] += 1
        home_stats['home_draws'] += 1
        home_stats['points'] += 1
        home_stats['recent_form'].append(1)
    else:
        home_stats['losses'] += 1
        home_stats['home_losses'] += 1
        home_stats['recent_form'].append(0)
        if away_goals - home_goals >= 2:
            home_stats['big_losses'] += 1
    
    if away_goals == 0:
        home_stats['clean_sheets'] += 1
    if home_goals == 0:
        home_stats['failed_to_score'] += 1
    
    # 更新客队统计
    away_stats = cumulative_stats[away_team]
    away_stats['games'] += 1
    away_stats['away_games'] += 1
    away_stats['goals_for'] += away_goals
    away_stats['goals_against'] += home_goals
    
    if away_goals > home_goals:
        away_stats['wins'] += 1
        away_stats['away_wins'] += 1
        away_stats['points'] += 3
        away_stats['recent_form'].append(3)
        if away_goals - home_goals >= 2:
            away_stats['big_wins'] += 1
    elif away_goals == home_goals:
        away_stats['draws'] += 1
        away_stats['away_draws'] += 1
        away_stats['points'] += 1
        away_stats['recent_form'].append(1)
    else:
        away_stats['losses'] += 1
        away_stats['away_losses'] += 1
        away_stats['recent_form'].append(0)
        if home_goals - away_goals >= 2:
            away_stats['big_losses'] += 1
    
    if home_goals == 0:
        away_stats['clean_sheets'] += 1
    if away_goals == 0:
        away_stats['failed_to_score'] += 1

def train_xgboost_model(match_features):
    """训练XGBoost模型"""
    print("🤖 训练XGBoost模型...")
    
    # 准备数据
    X = np.array([mf['features'] for mf in match_features])
    y = np.array([mf['label'] for mf in match_features])
    
    print(f"特征维度: {X.shape}")
    print(f"标签分布: {np.bincount(y)}")
    print(f"  主负(0): {np.sum(y==0)} 场")
    print(f"  平局(1): {np.sum(y==1)} 场") 
    print(f"  主胜(2): {np.sum(y==2)} 场")
    
    # 特征标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # XGBoost参数 - 强正则化
    xgb_params = {
        'objective': 'multi:softprob',
        'num_class': 3,
        'max_depth': 3,              # 限制树深度
        'learning_rate': 0.05,       # 低学习率
        'subsample': 0.7,            # 行采样
        'colsample_bytree': 0.7,     # 列采样
        'reg_alpha': 10,             # L1正则化
        'reg_lambda': 10,            # L2正则化
        'min_child_weight': 5,       # 最小子节点权重
        'gamma': 1,                  # 最小分裂损失
        'random_state': 42,
        'verbosity': 0
    }
    
    # 交叉验证
    print("🔄 进行交叉验证...")
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    # 转换为DMatrix
    dtrain = xgb.DMatrix(X_scaled, label=y)
    
    # 交叉验证
    cv_results = xgb.cv(
        xgb_params,
        dtrain,
        num_boost_round=200,
        nfold=5,
        stratified=True,
        shuffle=True,
        seed=42,
        early_stopping_rounds=20,
        verbose_eval=False
    )
    
    best_rounds = len(cv_results)
    best_score = cv_results['test-mlogloss-mean'].iloc[-1]
    
    print(f"最佳轮数: {best_rounds}")
    print(f"最佳CV分数: {best_score:.4f}")
    
    # 训练最终模型
    model = xgb.train(
        xgb_params,
        dtrain,
        num_boost_round=best_rounds
    )
    
    # 特征重要性
    importance = model.get_score(importance_type='weight')
    feature_names = [
        'home_elo', 'away_elo', 'elo_diff', 'elo_ratio',
        'home_points', 'away_points', 'points_diff', 'home_point_rate', 'away_point_rate',
        'home_goals_for', 'away_goals_for', 'home_goals_against', 'away_goals_against',
        'home_goal_diff', 'away_goal_diff', 'home_avg_goals_for', 'away_avg_goals_for',
        'home_avg_goals_against', 'away_avg_goals_against',
        'home_win_rate', 'away_win_rate', 'home_draw_rate', 'away_draw_rate',
        'home_loss_rate', 'away_loss_rate',
        'home_home_win_rate', 'away_away_win_rate', 'home_home_draw_rate', 'away_away_draw_rate',
        'home_home_loss_rate', 'away_away_loss_rate',
        'home_big_wins', 'away_big_wins', 'home_big_losses', 'away_big_losses',
        'home_clean_sheets', 'away_clean_sheets', 'home_failed_to_score', 'away_failed_to_score',
        'home_recent_form', 'away_recent_form'
    ]
    
    print("\n📊 特征重要性 TOP 10:")
    sorted_importance = sorted(importance.items(), key=lambda x: x[1], reverse=True)
    for i, (feature_idx, score) in enumerate(sorted_importance[:10]):
        feature_name = feature_names[int(feature_idx[1:])] if feature_idx.startswith('f') else feature_idx
        print(f"  {i+1:2d}. {feature_name:20s}: {score:3d}")
    
    return model, scaler, cv_results

def main():
    """主函数"""
    print("🚀 基于XGBoost的足球比赛预测模型")
    print("特点: 强正则化 + 55场数据一视同仁")
    print("="*50)
    
    # 加载数据
    matches = load_and_prepare_data()
    
    # 构建特征
    match_features, final_elo = build_cumulative_stats(matches)
    
    # 训练模型
    model, scaler, cv_results = train_xgboost_model(match_features)
    
    print(f"\n✅ 模型训练完成!")
    print(f"📊 训练样本: {len(match_features)} 场比赛")
    print(f"🎯 交叉验证分数: {cv_results['test-mlogloss-mean'].iloc[-1]:.4f}")
    print(f"💪 强正则化参数已应用，防止过拟合")
    
    # 保存模型
    model.save_model('xgboost_match_predictor.json')
    print(f"💾 模型已保存为 xgboost_match_predictor.json")
    
    return model, scaler, match_features, final_elo

if __name__ == "__main__":
    model, scaler, match_features, final_elo = main()
