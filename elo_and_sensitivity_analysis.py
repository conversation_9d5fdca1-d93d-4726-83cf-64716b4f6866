#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ELO评分系统 + 多权重敏感性分析
消除主观权重，提供更客观的预测
"""

import pandas as pd
import numpy as np
import random
import math
from collections import defaultdict
import itertools

def load_data():
    """加载数据"""
    df_stats = pd.read_csv('team_stats_for_prediction.csv', encoding='utf-8')
    df_results = pd.read_csv('football_matches_utf8.csv', encoding='utf-8')
    df_schedule = pd.read_csv('matches_utf8.csv', encoding='utf-8')
    return df_stats, df_results, df_schedule

class ELOSystem:
    """ELO评分系统"""
    
    def __init__(self, initial_rating=1500, k_factor=32):
        self.ratings = {}
        self.initial_rating = initial_rating
        self.k_factor = k_factor
        self.match_history = []
    
    def initialize_teams(self, teams):
        """初始化所有队伍的ELO评分"""
        for team in teams:
            self.ratings[team] = self.initial_rating
    
    def expected_score(self, rating_a, rating_b):
        """计算期望得分"""
        return 1 / (1 + 10**((rating_b - rating_a) / 400))
    
    def update_ratings(self, team_a, team_b, score_a, score_b):
        """更新ELO评分"""
        # 计算实际得分
        if score_a > score_b:
            actual_a, actual_b = 1.0, 0.0
        elif score_a < score_b:
            actual_a, actual_b = 0.0, 1.0
        else:
            actual_a, actual_b = 0.5, 0.5
        
        # 计算期望得分
        expected_a = self.expected_score(self.ratings[team_a], self.ratings[team_b])
        expected_b = 1 - expected_a
        
        # 更新评分
        new_rating_a = self.ratings[team_a] + self.k_factor * (actual_a - expected_a)
        new_rating_b = self.ratings[team_b] + self.k_factor * (actual_b - expected_b)
        
        self.ratings[team_a] = new_rating_a
        self.ratings[team_b] = new_rating_b
        
        # 记录历史
        self.match_history.append({
            'team_a': team_a, 'team_b': team_b,
            'score_a': score_a, 'score_b': score_b,
            'rating_a_before': self.ratings[team_a] - self.k_factor * (actual_a - expected_a),
            'rating_b_before': self.ratings[team_b] - self.k_factor * (actual_b - expected_b),
            'rating_a_after': new_rating_a,
            'rating_b_after': new_rating_b
        })
    
    def get_ratings(self):
        """获取当前评分"""
        return self.ratings.copy()

def build_elo_ratings(df_results):
    """基于比赛结果构建ELO评分"""
    print("🎯 构建ELO评分系统...")
    
    # 获取所有队伍
    teams = set()
    matches = []
    
    for _, row in df_results.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']) and pd.notna(row['参赛队伍1进球数']):
            team1, team2 = row['参赛队伍1'], row['参赛队伍2']
            goals1, goals2 = int(row['参赛队伍1进球数']), int(row['参赛队伍2进球数'])
            round_name = row['轮次']
            
            teams.add(team1)
            teams.add(team2)
            matches.append((round_name, team1, team2, goals1, goals2))
    
    # 按轮次排序比赛
    matches.sort(key=lambda x: x[0])
    
    # 初始化ELO系统
    elo = ELOSystem()
    elo.initialize_teams(teams)
    
    print(f"  初始化 {len(teams)} 支队伍，ELO评分均为 {elo.initial_rating}")
    
    # 逐场更新ELO评分
    for round_name, team1, team2, goals1, goals2 in matches:
        elo.update_ratings(team1, team2, goals1, goals2)
    
    final_ratings = elo.get_ratings()
    
    print("  ELO评分更新完成")
    print("  最终ELO评分 TOP 5:")
    sorted_ratings = sorted(final_ratings.items(), key=lambda x: x[1], reverse=True)
    for i, (team, rating) in enumerate(sorted_ratings[:5], 1):
        print(f"    {i}. {team}: {rating:.1f}")
    
    return final_ratings, elo

def calculate_strength_with_weights(df_stats, weights):
    """使用指定权重计算实力评分"""
    w_points, w_goal_diff, w_win_rate, w_stability = weights
    
    strengths = {}
    for _, row in df_stats.iterrows():
        team = row['队伍名称']
        
        # 各项指标标准化
        points_score = (row['积分'] / 30) * w_points * 100
        goal_diff_score = min(max((row['净胜球'] + 20) / 40, 0), 1) * w_goal_diff * 100
        win_rate_score = row['胜率'] * w_win_rate * 100
        stability_score = min(row['已赛场次'] / 10, 1) * w_stability * 100
        
        total_strength = points_score + goal_diff_score + win_rate_score + stability_score
        strengths[team] = total_strength
    
    return strengths

def sensitivity_analysis(df_stats, df_schedule):
    """多权重敏感性分析"""
    print("\n🔬 进行多权重敏感性分析...")
    
    # 定义不同的权重组合
    weight_combinations = [
        ([0.4, 0.25, 0.2, 0.15], "原始权重"),
        ([0.5, 0.2, 0.2, 0.1], "积分主导"),
        ([0.3, 0.4, 0.2, 0.1], "净胜球主导"),
        ([0.3, 0.2, 0.4, 0.1], "胜率主导"),
        ([0.33, 0.33, 0.33, 0.01], "平衡型"),
        ([0.6, 0.15, 0.15, 0.1], "极端积分主导"),
        ([0.2, 0.5, 0.2, 0.1], "极端净胜球主导"),
    ]
    
    results = {}
    
    for weights, name in weight_combinations:
        print(f"  测试权重组合: {name} {weights}")
        
        # 计算实力评分
        strengths = calculate_strength_with_weights(df_stats, weights)
        
        # 运行简化的蒙特卡洛模拟
        final_rankings = run_simplified_simulation(df_stats, df_schedule, strengths, 1000)
        
        # 计算期望排名
        expected_ranks = calculate_expected_rankings(final_rankings)
        results[name] = expected_ranks
    
    return results, weight_combinations

def run_simplified_simulation(df_stats, df_schedule, team_strengths, num_sims=1000):
    """简化的蒙特卡洛模拟"""
    # 获取剩余比赛
    completed_rounds = ['第1轮', '第2轮', '第3轮', '第4轮', '第5轮', '第6轮', '第7轮', '第8轮', '第9轮']
    remaining = df_schedule[~df_schedule['Round（轮次）'].isin(completed_rounds)]
    
    remaining_matches = []
    for _, row in remaining.iterrows():
        home_team = row['HomeTeam（主队）'].replace('市', '队')
        away_team = row['AwayTeam（客队）'].replace('市', '队')
        if home_team in team_strengths and away_team in team_strengths:
            remaining_matches.append((home_team, away_team))
    
    # 初始化当前积分
    current_points = {}
    for _, row in df_stats.iterrows():
        current_points[row['队伍名称']] = row['积分']
    
    final_rankings = []
    
    for sim in range(num_sims):
        sim_points = current_points.copy()
        
        # 模拟剩余比赛
        for home_team, away_team in remaining_matches:
            strength_diff = team_strengths[home_team] - team_strengths[away_team] + 3  # 主场优势
            win_prob = 1 / (1 + math.exp(-strength_diff / 15))
            draw_prob = 0.3 * math.exp(-(strength_diff ** 2) / 400)
            
            rand = random.random()
            if rand < win_prob:
                sim_points[home_team] += 3
            elif rand < win_prob + draw_prob:
                sim_points[home_team] += 1
                sim_points[away_team] += 1
            else:
                sim_points[away_team] += 3
        
        # 排序得到排名
        teams_sorted = sorted(sim_points.items(), key=lambda x: x[1], reverse=True)
        ranking = [team for team, _ in teams_sorted]
        final_rankings.append(ranking)
    
    return final_rankings

def calculate_expected_rankings(final_rankings):
    """计算期望排名"""
    position_counts = defaultdict(lambda: defaultdict(int))
    
    for ranking in final_rankings:
        for pos, team in enumerate(ranking, 1):
            position_counts[team][pos] += 1
    
    expected_ranks = {}
    for team in position_counts:
        total_sims = sum(position_counts[team].values())
        expected_rank = sum(pos * count for pos, count in position_counts[team].items()) / total_sims
        expected_ranks[team] = expected_rank
    
    return expected_ranks

def compare_methods(elo_ratings, sensitivity_results):
    """比较不同方法的结果"""
    print("\n📊 方法比较分析")
    print("="*60)
    
    # ELO排名
    elo_ranking = sorted(elo_ratings.items(), key=lambda x: x[1], reverse=True)
    print("\n🎯 ELO评分排名:")
    for i, (team, rating) in enumerate(elo_ranking, 1):
        print(f"  {i:2d}. {team:8s}: {rating:7.1f}")
    
    # 权重敏感性分析
    print("\n🔬 权重敏感性分析结果:")
    print("排名  队伍      ", end="")
    method_names = list(sensitivity_results.keys())
    for name in method_names:
        print(f"{name:12s}", end="")
    print()
    print("-" * (20 + 12 * len(method_names)))
    
    # 获取所有队伍
    all_teams = set()
    for results in sensitivity_results.values():
        all_teams.update(results.keys())
    
    # 按原始权重的排名排序
    original_results = sensitivity_results["原始权重"]
    teams_sorted = sorted(all_teams, key=lambda x: original_results.get(x, 99))
    
    for i, team in enumerate(teams_sorted, 1):
        print(f"{i:2d}    {team:8s}  ", end="")
        for name in method_names:
            rank = sensitivity_results[name].get(team, 99)
            print(f"{rank:8.1f}    ", end="")
        print()
    
    # 计算排名变化的标准差
    print("\n📈 排名稳定性分析 (标准差越小越稳定):")
    stability_scores = {}
    for team in all_teams:
        ranks = [sensitivity_results[method].get(team, 99) for method in method_names]
        std_dev = np.std(ranks)
        stability_scores[team] = std_dev
    
    sorted_stability = sorted(stability_scores.items(), key=lambda x: x[1])
    print("队伍        排名标准差")
    print("-" * 25)
    for team, std_dev in sorted_stability:
        stability = "稳定" if std_dev < 0.5 else "中等" if std_dev < 1.0 else "不稳定"
        print(f"{team:8s}    {std_dev:6.2f}  ({stability})")

def main():
    """主函数"""
    print("🚀 ELO评分系统 + 权重敏感性分析")
    print("目标: 消除主观权重，提供更客观的预测")
    print("="*50)
    
    # 加载数据
    df_stats, df_results, df_schedule = load_data()
    
    # 1. 构建ELO评分系统
    elo_ratings, elo_system = build_elo_ratings(df_results)
    
    # 2. 多权重敏感性分析
    sensitivity_results, weight_combinations = sensitivity_analysis(df_stats, df_schedule)
    
    # 3. 比较分析
    compare_methods(elo_ratings, sensitivity_results)
    
    # 4. 结论和建议
    print("\n💡 分析结论:")
    print("1. ELO评分完全基于比赛结果，无主观权重")
    print("2. 权重敏感性分析显示预测的稳健性")
    print("3. 排名标准差小的队伍预测更可靠")
    print("4. 可以选择ELO评分作为完全客观的替代方案")

if __name__ == "__main__":
    main()
