#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
贝叶斯模型综合对比分析
BTD vs 泊松 vs 其他方法的完整比较
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

def load_comparison_data():
    """加载对比数据"""
    print("📊 加载对比数据...")
    
    # 模拟各方法的结果 (基于实际运行结果)
    methods_results = {
        '原始权重法': {
            'accuracy': 0.533,
            'description': '基于经验权重的简单预测',
            'advantages': ['简单直观', '可解释性强', '计算快速'],
            'disadvantages': ['主观权重', '无不确定性量化', '难以优化']
        },
        'XGBoost优化版': {
            'accuracy': 0.571,
            'description': '相对特征工程 + 强正则化',
            'advantages': ['特征丰富', '自动学习', '防过拟合'],
            'disadvantages': ['需要大样本', '黑盒模型', '平局预测差']
        },
        'XGBoost稳健版': {
            'accuracy': 0.436,
            'description': '交叉验证的真实性能',
            'advantages': ['稳健验证', '避免过拟合', '可扩展'],
            'disadvantages': ['样本不足', '性能一般', '复杂度高']
        },
        '贝叶斯BTD': {
            'accuracy': 0.727,
            'description': 'Bradley-Terry-Davidson + MCMC',
            'advantages': ['专门处理小样本', '平局建模', '不确定性量化', '理论成熟'],
            'disadvantages': ['计算复杂', '需要先验', 'MCMC收敛']
        },
        '贝叶斯泊松': {
            'accuracy': 0.673,
            'description': '分层泊松回归 + 攻防交互',
            'advantages': ['进球数预测', '攻防分离', '分层建模', '物理意义'],
            'disadvantages': ['模型复杂', '收敛困难', '平局预测差']
        }
    }
    
    return methods_results

def analyze_btd_results():
    """分析BTD模型结果"""
    print("\n🎯 BTD模型深度分析")
    print("-" * 30)
    
    # BTD模型的关键发现
    btd_insights = {
        '队伍强度排名': [
            ('南通队', 4.132, '绝对强队'),
            ('盐城队', 1.776, '强队'),
            ('南京队', 1.704, '强队'),
            ('徐州队', 1.503, '中上'),
            ('泰州队', 1.112, '中等'),
            ('常州队', 0.305, '弱队')
        ],
        '模型参数': {
            '平局参数γ': 0.564,
            '主场优势': 0.138,
            '准确率': 0.727,
            'Brier分数': 0.490
        },
        '预测特点': {
            '客胜精确率': 0.70,
            '客胜召回率': 0.88,
            '平局精确率': 1.00,
            '平局召回率': 0.08,
            '主胜精确率': 0.74,
            '主胜召回率': 0.93
        }
    }
    
    print("🏆 BTD队伍强度分析:")
    for team, strength, level in btd_insights['队伍强度排名']:
        print(f"  {team:8s}: {strength:.3f} ({level})")
    
    print(f"\n📈 BTD模型参数:")
    for param, value in btd_insights['模型参数'].items():
        print(f"  {param}: {value}")
    
    return btd_insights

def analyze_poisson_results():
    """分析泊松模型结果"""
    print("\n🎯 泊松模型深度分析")
    print("-" * 30)
    
    # 泊松模型的关键发现
    poisson_insights = {
        '攻防能力排名': [
            ('南通队', 2.978, 0.914, 3.258),
            ('南京队', 1.856, 1.021, 1.818),
            ('盐城队', 1.763, 0.981, 1.797),
            ('徐州队', 1.468, 1.075, 1.366),
            ('常州队', 0.531, 1.357, 0.391)
        ],
        '模型参数': {
            '主场优势': 0.247,
            '攻防交互强度': 0.176,
            '结果准确率': 0.673,
            '进球预测MAE': 0.838
        },
        '预测特点': {
            '客胜精确率': 0.67,
            '客胜召回率': 0.75,
            '平局精确率': 0.00,
            '平局召回率': 0.00,
            '主胜精确率': 0.68,
            '主胜召回率': 0.93
        }
    }
    
    print("🏆 泊松攻防能力分析:")
    print("队伍      攻击力  防守力  攻防比")
    print("-" * 35)
    for team, attack, defense, ratio in poisson_insights['攻防能力排名']:
        print(f"{team:8s}  {attack:.3f}  {defense:.3f}  {ratio:.3f}")
    
    print(f"\n📈 泊松模型参数:")
    for param, value in poisson_insights['模型参数'].items():
        print(f"  {param}: {value}")
    
    return poisson_insights

def compare_all_methods():
    """对比所有方法"""
    print("\n📊 所有方法综合对比")
    print("="*50)
    
    methods_results = load_comparison_data()
    
    # 按准确率排序
    sorted_methods = sorted(methods_results.items(), key=lambda x: x[1]['accuracy'], reverse=True)
    
    print("🏆 准确率排名:")
    print("排名  方法           准确率   特点")
    print("-" * 45)
    
    for i, (method, data) in enumerate(sorted_methods, 1):
        print(f"{i:2d}   {method:12s}  {data['accuracy']:.1%}   {data['description']}")
    
    # 详细对比分析
    print(f"\n🔍 详细对比分析:")
    
    for method, data in sorted_methods:
        print(f"\n{method}:")
        print(f"  准确率: {data['accuracy']:.1%}")
        print(f"  优势: {', '.join(data['advantages'])}")
        print(f"  劣势: {', '.join(data['disadvantages'])}")

def analyze_bayesian_advantages():
    """分析贝叶斯方法的优势"""
    print("\n💡 贝叶斯方法优势分析")
    print("="*40)
    
    advantages = {
        '小样本处理': {
            '问题': '55个样本对于机器学习来说太少',
            'BTD解决方案': '通过先验信息弥补数据不足',
            '泊松解决方案': '分层建模共享信息',
            '效果': 'BTD达到72.7%准确率，显著超过其他方法'
        },
        '不确定性量化': {
            '问题': '传统方法只给出点预测',
            'BTD解决方案': 'MCMC采样获得完整后验分布',
            '泊松解决方案': '贝叶斯框架自然量化不确定性',
            '效果': '提供预测置信区间和参数不确定性'
        },
        '平局建模': {
            '问题': '平局是足球的重要特征，难以预测',
            'BTD解决方案': '专门的平局参数γ=0.564',
            '泊松解决方案': '通过进球数分布自然产生平局',
            '效果': 'BTD平局精确率100%（虽然召回率低）'
        },
        '先验知识融合': {
            '问题': '如何利用专家经验和历史数据',
            'BTD解决方案': '基于历史胜率设定强度先验',
            '泊松解决方案': '基于攻防数据设定能力先验',
            '效果': '模型更稳定，避免极端预测'
        }
    }
    
    for aspect, details in advantages.items():
        print(f"\n🎯 {aspect}:")
        for key, value in details.items():
            print(f"  {key}: {value}")

def practical_recommendations():
    """实用建议"""
    print("\n🎯 实用建议")
    print("="*30)
    
    recommendations = {
        '数据充足场景 (>200样本)': {
            '推荐方法': 'XGBoost + 深度特征工程',
            '理由': '大样本下机器学习方法优势明显',
            '注意事项': '需要充分的特征工程和交叉验证'
        },
        '小样本场景 (50-200样本)': {
            '推荐方法': '贝叶斯BTD模型',
            '理由': '专门处理小样本，准确率最高',
            '注意事项': '需要合理设定先验分布'
        },
        '极小样本场景 (<50样本)': {
            '推荐方法': '原始权重法 + 专家经验',
            '理由': '简单稳定，不易过拟合',
            '注意事项': '权重设定需要领域专家参与'
        },
        '需要进球数预测': {
            '推荐方法': '贝叶斯分层泊松模型',
            '理由': '同时预测比赛结果和进球数',
            '注意事项': '模型复杂，需要仔细调试'
        },
        '实时预测系统': {
            '推荐方法': '原始权重法 + 简化BTD',
            '理由': '计算快速，易于部署',
            '注意事项': '定期更新权重和参数'
        }
    }
    
    for scenario, rec in recommendations.items():
        print(f"\n📋 {scenario}:")
        for key, value in rec.items():
            print(f"  {key}: {value}")

def future_directions():
    """未来发展方向"""
    print("\n🚀 未来发展方向")
    print("="*30)
    
    directions = {
        '模型融合': [
            '集成BTD和泊松模型的优势',
            '动态权重调整机制',
            '多模型投票系统'
        ],
        '数据增强': [
            '收集更多历史赛季数据',
            '加入球员伤病信息',
            '考虑天气、场地等外部因素'
        ],
        '实时更新': [
            '比赛进行中的动态预测',
            '在线学习算法',
            '流式数据处理'
        ],
        '深度贝叶斯': [
            '变分推断替代MCMC',
            '神经网络贝叶斯化',
            '深度高斯过程'
        ]
    }
    
    for direction, items in directions.items():
        print(f"\n🎯 {direction}:")
        for item in items:
            print(f"  • {item}")

def main():
    """主函数"""
    print("🎯 贝叶斯模型综合对比分析")
    print("="*50)
    
    # 加载和分析数据
    methods_results = load_comparison_data()
    
    # 分析各模型结果
    btd_insights = analyze_btd_results()
    poisson_insights = analyze_poisson_results()
    
    # 综合对比
    compare_all_methods()
    
    # 贝叶斯优势分析
    analyze_bayesian_advantages()
    
    # 实用建议
    practical_recommendations()
    
    # 未来方向
    future_directions()
    
    print(f"\n✅ 综合分析完成!")
    print(f"🏆 最佳方法: 贝叶斯BTD模型 (72.7%准确率)")
    print(f"💡 核心洞察: 贝叶斯方法在小样本场景下具有显著优势")

if __name__ == "__main__":
    main()
