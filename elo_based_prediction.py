#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于ELO评分的完全客观预测系统
"""

import pandas as pd
import numpy as np
import random
import math
from collections import defaultdict

def load_data():
    """加载数据"""
    df_stats = pd.read_csv('team_stats_for_prediction.csv', encoding='utf-8')
    df_results = pd.read_csv('football_matches_utf8.csv', encoding='utf-8')
    df_schedule = pd.read_csv('matches_utf8.csv', encoding='utf-8')
    return df_stats, df_results, df_schedule

class ELOSystem:
    """ELO评分系统"""
    
    def __init__(self, initial_rating=1500, k_factor=32):
        self.ratings = {}
        self.initial_rating = initial_rating
        self.k_factor = k_factor
    
    def initialize_teams(self, teams):
        """初始化所有队伍的ELO评分"""
        for team in teams:
            self.ratings[team] = self.initial_rating
    
    def expected_score(self, rating_a, rating_b):
        """计算期望得分"""
        return 1 / (1 + 10**((rating_b - rating_a) / 400))
    
    def update_ratings(self, team_a, team_b, score_a, score_b):
        """更新ELO评分"""
        # 计算实际得分
        if score_a > score_b:
            actual_a, actual_b = 1.0, 0.0
        elif score_a < score_b:
            actual_a, actual_b = 0.0, 1.0
        else:
            actual_a, actual_b = 0.5, 0.5
        
        # 计算期望得分
        expected_a = self.expected_score(self.ratings[team_a], self.ratings[team_b])
        expected_b = 1 - expected_a
        
        # 更新评分
        new_rating_a = self.ratings[team_a] + self.k_factor * (actual_a - expected_a)
        new_rating_b = self.ratings[team_b] + self.k_factor * (actual_b - expected_b)
        
        self.ratings[team_a] = new_rating_a
        self.ratings[team_b] = new_rating_b
    
    def get_ratings(self):
        """获取当前评分"""
        return self.ratings.copy()

def build_elo_ratings(df_results):
    """基于比赛结果构建ELO评分"""
    # 获取所有队伍和比赛
    teams = set()
    matches = []
    
    for _, row in df_results.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']) and pd.notna(row['参赛队伍1进球数']):
            team1, team2 = row['参赛队伍1'], row['参赛队伍2']
            goals1, goals2 = int(row['参赛队伍1进球数']), int(row['参赛队伍2进球数'])
            round_name = row['轮次']
            
            teams.add(team1)
            teams.add(team2)
            matches.append((round_name, team1, team2, goals1, goals2))
    
    # 按轮次排序比赛
    matches.sort(key=lambda x: x[0])
    
    # 初始化ELO系统
    elo = ELOSystem()
    elo.initialize_teams(teams)
    
    # 逐场更新ELO评分
    for round_name, team1, team2, goals1, goals2 in matches:
        elo.update_ratings(team1, team2, goals1, goals2)
    
    return elo.get_ratings()

def calculate_elo_match_probabilities(rating_a, rating_b, home_advantage=50):
    """基于ELO评分计算对阵概率"""
    # 主场优势调整
    adjusted_rating_a = rating_a + home_advantage
    
    # ELO期望得分
    expected_a = 1 / (1 + 10**((rating_b - adjusted_rating_a) / 400))
    
    # 转换为胜平负概率
    # 这里使用经验公式，可以根据实际数据调整
    if expected_a > 0.6:
        win_prob = 0.4 + (expected_a - 0.6) * 1.0
        draw_prob = 0.35 - (expected_a - 0.6) * 0.5
    elif expected_a < 0.4:
        win_prob = expected_a * 1.0
        draw_prob = 0.35 - (0.4 - expected_a) * 0.5
    else:
        win_prob = expected_a
        draw_prob = 0.35
    
    loss_prob = 1 - win_prob - draw_prob
    
    # 确保概率有效
    total = win_prob + draw_prob + loss_prob
    return win_prob/total, draw_prob/total, loss_prob/total

def simulate_season_with_elo(df_stats, df_schedule, elo_ratings, num_simulations=10000):
    """基于ELO评分的蒙特卡洛模拟"""
    
    # 获取剩余比赛
    completed_rounds = ['第1轮', '第2轮', '第3轮', '第4轮', '第5轮', '第6轮', '第7轮', '第8轮', '第9轮']
    remaining = df_schedule[~df_schedule['Round（轮次）'].isin(completed_rounds)]
    
    remaining_matches = []
    for _, row in remaining.iterrows():
        home_team = row['HomeTeam（主队）'].replace('市', '队')
        away_team = row['AwayTeam（客队）'].replace('市', '队')
        if home_team in elo_ratings and away_team in elo_ratings:
            remaining_matches.append((home_team, away_team))
    
    # 初始化当前积分
    current_points = {}
    current_goal_diff = {}
    current_goals = {}
    
    for _, row in df_stats.iterrows():
        team = row['队伍名称']
        current_points[team] = row['积分']
        current_goal_diff[team] = row['净胜球']
        current_goals[team] = row['进球数']
    
    # 存储所有模拟结果
    final_rankings = []
    
    print(f"🎲 基于ELO评分进行 {num_simulations} 次蒙特卡洛模拟...")
    
    for sim in range(num_simulations):
        # 复制当前状态
        sim_points = current_points.copy()
        sim_goal_diff = current_goal_diff.copy()
        sim_goals = current_goals.copy()
        
        # 模拟剩余比赛
        for home_team, away_team in remaining_matches:
            # 基于ELO评分计算概率
            win_prob, draw_prob, loss_prob = calculate_elo_match_probabilities(
                elo_ratings[home_team], elo_ratings[away_team]
            )
            
            # 随机决定比赛结果
            rand = random.random()
            if rand < win_prob:
                # 主队胜
                sim_points[home_team] += 3
                goal_diff = random.choice([1, 2, 3])
                sim_goal_diff[home_team] += goal_diff
                sim_goal_diff[away_team] -= goal_diff
                sim_goals[home_team] += goal_diff + random.choice([0, 1])
            elif rand < win_prob + draw_prob:
                # 平局
                sim_points[home_team] += 1
                sim_points[away_team] += 1
                goals = random.choice([0, 1, 2])
                sim_goals[home_team] += goals
                sim_goals[away_team] += goals
            else:
                # 客队胜
                sim_points[away_team] += 3
                goal_diff = random.choice([1, 2, 3])
                sim_goal_diff[away_team] += goal_diff
                sim_goal_diff[home_team] -= goal_diff
                sim_goals[away_team] += goal_diff + random.choice([0, 1])
        
        # 计算最终排名
        teams_final = []
        for team in sim_points.keys():
            teams_final.append((team, sim_points[team], sim_goal_diff[team], sim_goals[team]))
        
        # 按积分、净胜球、进球数排序
        teams_final.sort(key=lambda x: (x[1], x[2], x[3]), reverse=True)
        
        # 记录排名
        ranking = [team[0] for team in teams_final]
        final_rankings.append(ranking)
        
        if (sim + 1) % 1000 == 0:
            print(f"  完成 {sim + 1} 次模拟...")
    
    return final_rankings

def analyze_elo_results(final_rankings, elo_ratings):
    """分析ELO预测结果"""
    num_teams = len(final_rankings[0])
    num_sims = len(final_rankings)
    
    # 统计每个位置的概率
    position_probs = defaultdict(lambda: defaultdict(int))
    
    for ranking in final_rankings:
        for pos, team in enumerate(ranking, 1):
            position_probs[team][pos] += 1
    
    # 转换为概率
    for team in position_probs:
        for pos in position_probs[team]:
            position_probs[team][pos] = position_probs[team][pos] / num_sims
    
    return position_probs

def print_elo_results(position_probs, elo_ratings):
    """打印ELO预测结果"""
    print("\n" + "="*60)
    print("🏆 基于ELO评分的最终排名预测")
    print("="*60)
    
    # 计算期望排名
    expected_rankings = []
    for team in position_probs:
        expected_rank = sum(pos * prob for pos, prob in position_probs[team].items())
        expected_rankings.append((team, expected_rank, elo_ratings.get(team, 0)))
    
    expected_rankings.sort(key=lambda x: x[1])
    
    print("\n📊 ELO预测最终排名:")
    print("排名  队伍      期望排名  ELO评分   前三概率  保级概率")
    print("-" * 55)
    
    for i, (team, exp_rank, elo_rating) in enumerate(expected_rankings, 1):
        # 计算前三概率
        top3_prob = sum(position_probs[team].get(pos, 0) for pos in [1, 2, 3])
        # 计算保级概率 (前10名)
        safe_prob = sum(position_probs[team].get(pos, 0) for pos in range(1, 11))
        
        print(f"{i:2d}   {team:8s}  {exp_rank:6.1f}    {elo_rating:7.1f}   {top3_prob:6.1%}    {safe_prob:6.1%}")
    
    # 冠军争夺分析
    print(f"\n🥇 冠军争夺分析 (ELO版本):")
    champion_probs = [(team, position_probs[team].get(1, 0)) for team in position_probs]
    champion_probs.sort(key=lambda x: x[1], reverse=True)
    
    for i, (team, prob) in enumerate(champion_probs[:5]):
        if prob > 0.001:
            print(f"  {i+1}. {team:8s}: {prob:6.1%}")
    
    # 保级大战分析
    print(f"\n⚠️  保级大战分析 (倒数三名概率):")
    relegation_probs = []
    for team in position_probs:
        prob = sum(position_probs[team].get(pos, 0) for pos in [11, 12, 13])
        relegation_probs.append((team, prob))
    
    relegation_probs.sort(key=lambda x: x[1], reverse=True)
    for i, (team, prob) in enumerate(relegation_probs[:8]):
        if prob > 0.01:
            print(f"  {team:8s}: {prob:6.1%}")

def main():
    """主函数"""
    print("🚀 基于ELO评分的完全客观预测")
    print("优势: 无主观权重，完全基于比赛结果")
    print("="*50)
    
    # 加载数据
    df_stats, df_results, df_schedule = load_data()
    
    # 构建ELO评分
    elo_ratings = build_elo_ratings(df_results)
    
    print("\n📊 当前ELO评分排名:")
    elo_ranking = sorted(elo_ratings.items(), key=lambda x: x[1], reverse=True)
    for i, (team, rating) in enumerate(elo_ranking, 1):
        print(f"  {i:2d}. {team:8s}: {rating:7.1f}")
    
    # 蒙特卡洛模拟
    final_rankings = simulate_season_with_elo(df_stats, df_schedule, elo_ratings, 5000)
    
    # 分析结果
    position_probs = analyze_elo_results(final_rankings, elo_ratings)
    
    # 打印结果
    print_elo_results(position_probs, elo_ratings)
    
    print("\n💡 ELO方法的优势:")
    print("✅ 完全客观，无主观权重设置")
    print("✅ 基于实际比赛结果动态更新")
    print("✅ 考虑了对手实力的相对强弱")
    print("✅ 国际象棋、网球等领域的成熟方法")

if __name__ == "__main__":
    main()
