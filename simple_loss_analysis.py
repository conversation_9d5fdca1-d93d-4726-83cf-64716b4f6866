#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版：专门计算预测"输"的准确率
"""

import pandas as pd
import numpy as np
import math

def load_data():
    """加载数据"""
    df_stats = pd.read_csv('team_stats_for_prediction_updated.csv', encoding='utf-8')
    df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
    df_schedule = pd.read_csv('maches2_utf8.csv', encoding='utf-8')
    return df_stats, df_results, df_schedule

def calculate_team_strength_at_round(df_results, target_round):
    """计算指定轮次后的队伍实力"""
    target_rounds = [f'第{i}轮' for i in range(1, target_round + 1)]
    if target_round >= 4:
        target_rounds.append('第4轮(补赛)')
    
    completed_matches = df_results[df_results['轮次'].isin(target_rounds)]
    
    teams = set()
    team_stats = {}
    
    for _, row in completed_matches.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
            teams.add(row['参赛队伍1'])
            teams.add(row['参赛队伍2'])
    
    for team in teams:
        team_stats[team] = {'已赛场次': 0, '胜场': 0, '平场': 0, '负场': 0, '进球数': 0, '失球数': 0, '积分': 0}
    
    for _, row in completed_matches.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
            goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
            goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
            
            if pd.notna(row[goals1_col]):
                team1, team2 = row['参赛队伍1'], row['参赛队伍2']
                goals1, goals2 = int(row[goals1_col]), int(row[goals2_col])
                
                team_stats[team1]['已赛场次'] += 1
                team_stats[team2]['已赛场次'] += 1
                team_stats[team1]['进球数'] += goals1
                team_stats[team1]['失球数'] += goals2
                team_stats[team2]['进球数'] += goals2
                team_stats[team2]['失球数'] += goals1
                
                if goals1 > goals2:
                    team_stats[team1]['胜场'] += 1
                    team_stats[team1]['积分'] += 3
                    team_stats[team2]['负场'] += 1
                elif goals1 < goals2:
                    team_stats[team2]['胜场'] += 1
                    team_stats[team2]['积分'] += 3
                    team_stats[team1]['负场'] += 1
                else:
                    team_stats[team1]['平场'] += 1
                    team_stats[team1]['积分'] += 1
                    team_stats[team2]['平场'] += 1
                    team_stats[team2]['积分'] += 1
    
    strengths = {}
    for team in teams:
        stats = team_stats[team]
        if stats['已赛场次'] > 0:
            points_score = (stats['积分'] / (stats['已赛场次'] * 3)) * 40
            net_goals = stats['进球数'] - stats['失球数']
            goal_diff_score = min(max((net_goals + 10) / 20, 0), 1) * 25
            win_rate = stats['胜场'] / stats['已赛场次']
            win_rate_score = win_rate * 20
            stability_score = min(stats['已赛场次'] / 8, 1) * 15
            
            total_strength = points_score + goal_diff_score + win_rate_score + stability_score
            strengths[team] = total_strength
        else:
            strengths[team] = 50
    
    return strengths

def calculate_match_probabilities(team_a, team_b, strength_a, strength_b):
    """计算对阵概率"""
    strength_diff = strength_a - strength_b + 3  # 主场优势
    win_prob = 1 / (1 + math.exp(-strength_diff / 15))
    draw_prob = 0.3 * math.exp(-(strength_diff ** 2) / 400)
    loss_prob = 1 - win_prob - draw_prob
    
    total = win_prob + draw_prob + loss_prob
    return win_prob/total, draw_prob/total, loss_prob/total

def analyze_loss_predictions():
    """分析预测输的准确率"""
    print("🎯 分析预测'输'的准确率")
    print("="*50)
    
    df_stats, df_results, df_schedule = load_data()
    
    # 验证轮次
    validation_rounds = [(3, 4), (4, 5), (5, 6), (6, 7), (7, 8)]
    
    # 统计变量
    total_loss_predictions = 0  # 总的输的预测次数
    correct_loss_predictions = 0  # 正确的输的预测次数
    
    total_actual_losses = 0  # 总的实际输的次数
    predicted_actual_losses = 0  # 实际输中被正确预测的次数
    
    all_results = []
    
    for train_round, test_round in validation_rounds:
        print(f"\n📊 第{test_round}轮分析:")
        
        # 计算实力
        team_strengths = calculate_team_strength_at_round(df_results, train_round)
        
        # 获取该轮比赛
        round_schedule = df_schedule[df_schedule['Round（轮次）'] == f'第{test_round}轮']
        round_results = df_results[df_results['轮次'] == f'第{test_round}轮']
        
        round_loss_pred = 0
        round_correct_loss = 0
        round_actual_loss = 0
        round_predicted_actual_loss = 0
        
        for _, schedule_row in round_schedule.iterrows():
            home_team = schedule_row['HomeTeam（主队）'].replace('市', '队')
            away_team = schedule_row['AwayTeam（客队）'].replace('市', '队')
            
            if home_team in team_strengths and away_team in team_strengths:
                # 预测概率
                win_prob, draw_prob, loss_prob = calculate_match_probabilities(
                    home_team, away_team, 
                    team_strengths[home_team], team_strengths[away_team]
                )
                
                # 预测结果
                if win_prob > draw_prob and win_prob > loss_prob:
                    predicted = "主胜"
                elif draw_prob > loss_prob:
                    predicted = "平局"
                else:
                    predicted = "客胜"
                
                # 找到实际结果
                actual = None
                for _, result_row in round_results.iterrows():
                    if result_row['参赛队伍1'] == home_team and result_row['参赛队伍2'] == away_team:
                        goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in result_row else '参赛队伍1进球 数'
                        goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in result_row else '参赛队伍2进球数'
                        
                        if pd.notna(result_row[goals1_col]):
                            goals1, goals2 = int(result_row[goals1_col]), int(result_row[goals2_col])
                            if goals1 > goals2:
                                actual = "主胜"
                            elif goals1 < goals2:
                                actual = "客胜"
                            else:
                                actual = "平局"
                            break
                
                if actual:
                    all_results.append({
                        'round': test_round,
                        'home_team': home_team,
                        'away_team': away_team,
                        'predicted': predicted,
                        'actual': actual,
                        'win_prob': win_prob,
                        'draw_prob': draw_prob,
                        'loss_prob': loss_prob
                    })
                    
                    # 统计预测"输"的情况
                    if predicted in ["主胜", "客胜"]:  # 预测有队伍输
                        total_loss_predictions += 1
                        round_loss_pred += 1
                        if predicted == actual:  # 预测正确
                            correct_loss_predictions += 1
                            round_correct_loss += 1
                    
                    # 统计实际"输"的情况
                    if actual in ["主胜", "客胜"]:  # 实际有队伍输
                        total_actual_losses += 1
                        round_actual_loss += 1
                        if predicted == actual:  # 被正确预测
                            predicted_actual_losses += 1
                            round_predicted_actual_loss += 1
        
        # 打印本轮统计
        if round_loss_pred > 0:
            round_precision = round_correct_loss / round_loss_pred
            print(f"  预测输的准确率: {round_precision:.1%} ({round_correct_loss}/{round_loss_pred})")
        
        if round_actual_loss > 0:
            round_recall = round_predicted_actual_loss / round_actual_loss
            print(f"  实际输的召回率: {round_recall:.1%} ({round_predicted_actual_loss}/{round_actual_loss})")
    
    # 总体统计
    print(f"\n🏆 总体统计:")
    print(f"="*30)
    
    if total_loss_predictions > 0:
        precision = correct_loss_predictions / total_loss_predictions
        print(f"预测'输'的准确率 (精确率): {precision:.1%}")
        print(f"  - 预测有队伍输的次数: {total_loss_predictions}")
        print(f"  - 预测正确的次数: {correct_loss_predictions}")
    
    if total_actual_losses > 0:
        recall = predicted_actual_losses / total_actual_losses
        print(f"实际'输'的召回率: {recall:.1%}")
        print(f"  - 实际有队伍输的次数: {total_actual_losses}")
        print(f"  - 被正确预测的次数: {predicted_actual_losses}")
    
    # 详细结果
    print(f"\n📋 详细预测结果:")
    print("轮次  主队    vs 客队    预测    实际    主胜率  平局率  客胜率")
    print("-" * 65)
    
    for result in all_results:
        status = "✅" if result['predicted'] == result['actual'] else "❌"
        print(f"{result['round']:2d}   {result['home_team']:6s} vs {result['away_team']:6s}  {result['predicted']:4s}  {result['actual']:4s}  {result['win_prob']:.1%}   {result['draw_prob']:.1%}   {result['loss_prob']:.1%} {status}")
    
    return precision if total_loss_predictions > 0 else 0, recall if total_actual_losses > 0 else 0

def main():
    """主函数"""
    precision, recall = analyze_loss_predictions()
    
    print(f"\n💡 结论:")
    print(f"  • 预测'输'的准确率: {precision:.1%}")
    print(f"  • 实际'输'的召回率: {recall:.1%}")
    
    if precision > 0.5:
        print(f"  ✅ 预测'输'的准确率超过50%，表现良好")
    else:
        print(f"  ⚠️  预测'输'的准确率低于50%，有改进空间")

if __name__ == "__main__":
    main()
