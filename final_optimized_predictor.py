#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化的XGBoost预测模型
重点解决平局预测问题 + 进一步提升准确率
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.feature_selection import SelectKBest, mutual_info_classif
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
from sklearn.utils.class_weight import compute_class_weight
from sklearn.impute import SimpleImputer
import warnings
warnings.filterwarnings('ignore')

class FinalOptimizedPredictor:
    """最终优化的预测器"""
    
    def __init__(self):
        self.model = None
        self.scaler = RobustScaler()
        self.imputer = SimpleImputer(strategy='median')
        self.feature_selector = SelectKBest(score_func=mutual_info_classif, k=12)  # 减少特征数避免过拟合
        self.feature_names = []
        
        # 进一步优化的参数，专门处理不平衡数据
        self.params = {
            'objective': 'multi:softprob',
            'num_class': 3,
            'max_depth': 4,  # 减少深度
            'learning_rate': 0.05,  # 降低学习率
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 1.0,  # 增加正则化
            'reg_lambda': 1.0,
            'min_child_weight': 3,
            'gamma': 0.2,
            'random_state': 42,
            'verbosity': 0,
            'eval_metric': 'mlogloss',
            'scale_pos_weight': 1  # 处理类别不平衡
        }
    
    def load_and_clean_data(self):
        """加载并清理数据"""
        print("📊 加载并清理比赛数据...")
        
        df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
        
        valid_matches = []
        for _, row in df_results.iterrows():
            if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
                goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
                goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
                
                if pd.notna(row[goals1_col]) and pd.notna(row[goals2_col]):
                    valid_matches.append({
                        'round': row['轮次'],
                        'home_team': row['参赛队伍1'],
                        'away_team': row['参赛队伍2'],
                        'home_goals': int(row[goals1_col]),
                        'away_goals': int(row[goals2_col])
                    })
        
        print(f"有效比赛数据: {len(valid_matches)} 场")
        return valid_matches
    
    def extract_enhanced_features(self, home_team, away_team, elo_ratings, team_profiles):
        """提取增强的相对特征，特别关注平局预测"""
        home_profile = team_profiles[home_team]
        away_profile = team_profiles[away_team]
        
        def safe_divide(a, b, default=0):
            return a / b if b > 0 else default
        
        features = []
        feature_names = []
        
        # 1. 核心实力对比特征
        home_elo = elo_ratings[home_team]
        away_elo = elo_ratings[away_team]
        elo_diff = home_elo - away_elo
        
        features.extend([
            elo_diff / 100,  # 标准化ELO差值
            abs(elo_diff) / 100,  # ELO差值绝对值（实力接近程度）
            1 / (1 + abs(elo_diff) / 100)  # 实力接近度（越接近1越可能平局）
        ])
        feature_names.extend(['elo_diff_norm', 'elo_abs_diff', 'strength_similarity'])
        
        # 2. 积分和状态对比
        home_games = max(home_profile['games'], 1)
        away_games = max(away_profile['games'], 1)
        
        home_ppg = safe_divide(home_profile['points'], home_games)
        away_ppg = safe_divide(away_profile['points'], away_games)
        ppg_diff = home_ppg - away_ppg
        
        features.extend([
            ppg_diff,  # 场均积分差
            abs(ppg_diff),  # 场均积分差绝对值
            1 / (1 + abs(ppg_diff))  # 积分接近度
        ])
        feature_names.extend(['ppg_diff', 'ppg_abs_diff', 'ppg_similarity'])
        
        # 3. 攻防平衡特征（关键）
        home_gpg = safe_divide(home_profile['goals_for'], home_games)
        away_gpg = safe_divide(away_profile['goals_for'], away_games)
        home_gapg = safe_divide(home_profile['goals_against'], home_games)
        away_gapg = safe_divide(away_profile['goals_against'], away_games)
        
        # 攻防匹配度（预测平局的关键特征）
        home_attack_vs_away_defense = home_gpg / max(away_gapg, 0.1)
        away_attack_vs_home_defense = away_gpg / max(home_gapg, 0.1)
        attack_defense_balance = abs(home_attack_vs_away_defense - away_attack_vs_home_defense)
        
        features.extend([
            home_attack_vs_away_defense,
            away_attack_vs_home_defense,
            1 / (1 + attack_defense_balance),  # 攻防平衡度（越高越可能平局）
            abs(home_gpg - away_gpg),  # 进攻差距
            abs(home_gapg - away_gapg)  # 防守差距
        ])
        feature_names.extend(['home_att_vs_away_def', 'away_att_vs_home_def', 'attack_defense_balance', 'attack_gap', 'defense_gap'])
        
        # 4. 主客场优势特征
        home_home_games = max(home_profile['home_games'], 1)
        away_away_games = max(away_profile['away_games'], 1)
        
        home_home_ppg = safe_divide(home_profile['home_wins'] * 3 + home_profile['home_draws'], home_home_games)
        away_away_ppg = safe_divide(away_profile['away_wins'] * 3 + away_profile['away_draws'], away_away_games)
        
        home_advantage = home_home_ppg - home_ppg  # 主场优势
        away_disadvantage = away_ppg - away_away_ppg  # 客场劣势
        
        features.extend([
            home_advantage,
            away_disadvantage,
            home_advantage + away_disadvantage  # 总主场效应
        ])
        feature_names.extend(['home_advantage', 'away_disadvantage', 'total_home_effect'])
        
        # 5. 最近状态和趋势
        if home_profile['recent_results'] and away_profile['recent_results']:
            home_recent_form = np.mean(home_profile['recent_results'])
            away_recent_form = np.mean(away_profile['recent_results'])
            form_diff = home_recent_form - away_recent_form
            
            # 状态稳定性（方差小的队伍更稳定）
            home_form_stability = 1 / (1 + np.var(home_profile['recent_results']) + 0.1)
            away_form_stability = 1 / (1 + np.var(away_profile['recent_results']) + 0.1)
            
            features.extend([
                form_diff,
                abs(form_diff),  # 状态差距
                (home_form_stability + away_form_stability) / 2  # 平均稳定性
            ])
            feature_names.extend(['form_diff', 'form_abs_diff', 'avg_stability'])
        else:
            features.extend([0, 0, 0.5])
            feature_names.extend(['form_diff', 'form_abs_diff', 'avg_stability'])
        
        # 6. 平局倾向特征
        home_draw_rate = safe_divide(home_profile['draws'], home_games)
        away_draw_rate = safe_divide(away_profile['draws'], away_games)
        
        features.extend([
            (home_draw_rate + away_draw_rate) / 2,  # 平均平局率
            min(home_draw_rate, away_draw_rate),  # 最小平局率
            max(home_draw_rate, away_draw_rate)   # 最大平局率
        ])
        feature_names.extend(['avg_draw_rate', 'min_draw_rate', 'max_draw_rate'])
        
        return features, feature_names
    
    def build_enhanced_dataset(self, matches):
        """构建增强数据集"""
        print("🔧 构建增强特征数据集...")
        
        # 初始化
        teams = set()
        for match in matches:
            teams.add(match['home_team'])
            teams.add(match['away_team'])
        
        teams = sorted(list(teams))
        elo_ratings = {team: 1500 for team in teams}
        team_profiles = {}
        
        for team in teams:
            team_profiles[team] = {
                'games': 0, 'wins': 0, 'draws': 0, 'losses': 0,
                'goals_for': 0, 'goals_against': 0, 'points': 0,
                'home_games': 0, 'home_wins': 0, 'home_draws': 0, 'home_losses': 0,
                'away_games': 0, 'away_wins': 0, 'away_draws': 0, 'away_losses': 0,
                'recent_results': []
            }
        
        matches_sorted = sorted(matches, key=lambda x: x['round'])
        X, y = [], []
        
        for match in matches_sorted:
            home_team = match['home_team']
            away_team = match['away_team']
            home_goals = match['home_goals']
            away_goals = match['away_goals']
            
            # 提取特征
            features, feature_names = self.extract_enhanced_features(home_team, away_team, elo_ratings, team_profiles)
            X.append(features)
            
            # 标签
            if home_goals > away_goals:
                y.append(2)  # 主胜
            elif home_goals < away_goals:
                y.append(0)  # 主负
            else:
                y.append(1)  # 平局
            
            # 更新数据
            self.update_profiles(team_profiles, elo_ratings, home_team, away_team, home_goals, away_goals)
        
        self.feature_names = feature_names
        return np.array(X), np.array(y)
    
    def update_profiles(self, team_profiles, elo_ratings, home_team, away_team, home_goals, away_goals):
        """更新队伍数据"""
        # 更新ELO
        k_factor = 32
        if home_goals > away_goals:
            actual_home, actual_away = 1.0, 0.0
        elif home_goals < away_goals:
            actual_home, actual_away = 0.0, 1.0
        else:
            actual_home, actual_away = 0.5, 0.5
        
        expected_home = 1 / (1 + 10**((elo_ratings[away_team] - elo_ratings[home_team]) / 400))
        expected_away = 1 - expected_home
        
        elo_ratings[home_team] += k_factor * (actual_home - expected_home)
        elo_ratings[away_team] += k_factor * (actual_away - expected_away)
        
        # 更新统计
        for team, goals_for, goals_against, is_home in [
            (home_team, home_goals, away_goals, True),
            (away_team, away_goals, home_goals, False)
        ]:
            profile = team_profiles[team]
            profile['games'] += 1
            profile['goals_for'] += goals_for
            profile['goals_against'] += goals_against
            
            if is_home:
                profile['home_games'] += 1
            else:
                profile['away_games'] += 1
            
            if goals_for > goals_against:
                profile['wins'] += 1
                profile['points'] += 3
                if is_home:
                    profile['home_wins'] += 1
                else:
                    profile['away_wins'] += 1
                profile['recent_results'].append(3)
            elif goals_for == goals_against:
                profile['draws'] += 1
                profile['points'] += 1
                if is_home:
                    profile['home_draws'] += 1
                else:
                    profile['away_draws'] += 1
                profile['recent_results'].append(1)
            else:
                profile['losses'] += 1
                if is_home:
                    profile['home_losses'] += 1
                else:
                    profile['away_losses'] += 1
                profile['recent_results'].append(0)
            
            # 保持最近5场
            if len(profile['recent_results']) > 5:
                profile['recent_results'] = profile['recent_results'][-5:]
    
    def train_with_class_weights(self, X, y):
        """使用类别权重训练模型"""
        print("🚀 训练带类别权重的XGBoost模型...")
        
        # 计算类别权重
        class_weights = compute_class_weight('balanced', classes=np.unique(y), y=y)
        print(f"类别权重: 主负{class_weights[0]:.2f}, 平局{class_weights[1]:.2f}, 主胜{class_weights[2]:.2f}")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 创建样本权重
        sample_weights = np.array([class_weights[label] for label in y_train])
        
        # 创建DMatrix
        dtrain = xgb.DMatrix(X_train, label=y_train, weight=sample_weights)
        dtest = xgb.DMatrix(X_test, label=y_test)
        
        # 训练模型
        self.model = xgb.train(
            self.params,
            dtrain,
            num_boost_round=150,
            evals=[(dtrain, 'train'), (dtest, 'test')],
            early_stopping_rounds=20,
            verbose_eval=False
        )
        
        # 评估
        test_probs = self.model.predict(dtest)
        test_preds = np.argmax(test_probs, axis=1)
        test_acc = accuracy_score(y_test, test_preds)
        
        print(f"\n📊 模型性能:")
        print(f"测试准确率: {test_acc:.3f}")
        print(f"预测概率范围: [{test_probs.min():.3f}, {test_probs.max():.3f}]")
        
        print(f"\n🎯 详细分类报告:")
        print(classification_report(y_test, test_preds, target_names=['客胜', '平局', '主胜']))
        
        print(f"\n📈 混淆矩阵:")
        cm = confusion_matrix(y_test, test_preds)
        print(cm)
        
        return test_acc
    
    def run_final_pipeline(self):
        """运行最终优化流程"""
        print("🏁 运行最终优化的预测流程")
        print("="*50)
        
        # 1. 加载数据
        matches = self.load_and_clean_data()
        
        # 2. 构建增强数据集
        X, y = self.build_enhanced_dataset(matches)
        
        # 3. 数据预处理
        print("🧹 数据预处理...")
        X_imputed = self.imputer.fit_transform(X)
        X_scaled = self.scaler.fit_transform(X_imputed)
        X_selected = self.feature_selector.fit_transform(X_scaled, y)
        
        selected_indices = self.feature_selector.get_support(indices=True)
        selected_features = [self.feature_names[i] for i in selected_indices]
        
        print(f"原始特征数: {X.shape[1]}")
        print(f"选择特征数: {X_selected.shape[1]}")
        print(f"选中的特征: {selected_features}")
        
        # 4. 训练模型
        test_accuracy = self.train_with_class_weights(X_selected, y)
        
        print(f"\n✅ 最终优化完成!")
        print(f"🎯 最终测试准确率: {test_accuracy:.1%}")
        
        return test_accuracy

def main():
    """主函数"""
    predictor = FinalOptimizedPredictor()
    accuracy = predictor.run_final_pipeline()
    
    print(f"\n💡 最终优化要点:")
    print(f"✅ 增强特征工程: 专门设计平局预测特征")
    print(f"✅ 类别权重: 解决数据不平衡问题")
    print(f"✅ 攻防平衡: 关注攻防匹配度")
    print(f"✅ 实力接近度: 量化队伍实力相似性")
    print(f"✅ 状态稳定性: 考虑队伍状态波动")

if __name__ == "__main__":
    main()
