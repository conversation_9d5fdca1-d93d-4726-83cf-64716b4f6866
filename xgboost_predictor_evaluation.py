#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XGBoost模型的预测和评估
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import pickle
import random
import math

def load_model_and_data():
    """加载模型和数据"""
    print("📥 加载训练好的模型和数据...")
    
    # 重新运行训练脚本获取数据
    exec(open('xgboost_match_predictor.py').read())
    
    # 加载保存的模型
    model = xgb.Booster()
    model.load_model('xgboost_match_predictor.json')
    
    return model

def predict_remaining_matches():
    """预测剩余比赛"""
    print("🔮 预测剩余比赛...")
    
    # 加载当前数据
    df_schedule = pd.read_csv('maches2_utf8.csv', encoding='utf-8')
    df_stats = pd.read_csv('team_stats_for_prediction_updated.csv', encoding='utf-8')
    
    # 获取剩余比赛
    completed_rounds = ['第1轮', '第2轮', '第3轮', '第4轮', '第5轮', '第6轮', '第7轮', '第8轮', '第9轮', '第4轮(补赛)']
    remaining = df_schedule[~df_schedule['Round（轮次）'].isin(completed_rounds)]
    
    print(f"剩余比赛: {len(remaining)} 场")
    
    # 获取当前ELO评分 (简化版本)
    current_elo = {}
    for _, row in df_stats.iterrows():
        team = row['队伍名称']
        # 基于当前表现估算ELO
        points_rate = row['积分'] / (row['已赛场次'] * 3) if row['已赛场次'] > 0 else 0
        estimated_elo = 1500 + (points_rate - 0.5) * 400
        current_elo[team] = estimated_elo
    
    # 获取当前统计数据
    current_stats = {}
    for _, row in df_stats.iterrows():
        team = row['队伍名称']
        current_stats[team] = {
            'games': row['已赛场次'],
            'wins': row['胜场'],
            'draws': row['平场'], 
            'losses': row['负场'],
            'goals_for': row['进球数'],
            'goals_against': row['失球数'],
            'points': row['积分'],
            'home_games': row['主场胜场'] + row['主场平场'] + row['主场负场'],
            'home_wins': row['主场胜场'],
            'home_draws': row['主场平场'],
            'home_losses': row['主场负场'],
            'away_games': row['客场胜场'] + row['客场平场'] + row['客场负场'],
            'away_wins': row['客场胜场'],
            'away_draws': row['客场平场'],
            'away_losses': row['客场负场'],
            'recent_form': [1] * 3,  # 简化
            'big_wins': row['大胜场次'],
            'big_losses': row['大败场次'],
            'clean_sheets': row['零封次数'],
            'failed_to_score': row['被零封次数']
        }
    
    # 加载模型
    model = xgb.Booster()
    model.load_model('xgboost_match_predictor.json')
    
    # 预测剩余比赛
    predictions = []
    
    for _, row in remaining.iterrows():
        home_team = row['HomeTeam（主队）'].replace('市', '队')
        away_team = row['AwayTeam（客队）'].replace('市', '队')
        
        if home_team in current_elo and away_team in current_elo:
            # 提取特征
            features = extract_current_features(home_team, away_team, current_elo, current_stats)
            
            # 预测
            dtest = xgb.DMatrix([features])
            probs = model.predict(dtest)[0]
            
            predicted_class = np.argmax(probs)
            confidence = np.max(probs)
            
            result_map = {0: "客胜", 1: "平局", 2: "主胜"}
            
            predictions.append({
                'round': row['Round（轮次）'],
                'home_team': home_team,
                'away_team': away_team,
                'predicted_result': result_map[predicted_class],
                'confidence': confidence,
                'prob_away_win': probs[0],
                'prob_draw': probs[1], 
                'prob_home_win': probs[2]
            })
    
    return predictions

def extract_current_features(home_team, away_team, current_elo, current_stats):
    """提取当前特征"""
    features = []
    
    # ELO特征
    home_elo = current_elo[home_team]
    away_elo = current_elo[away_team]
    
    features.extend([
        home_elo,
        away_elo,
        home_elo - away_elo,
        home_elo / (home_elo + away_elo)
    ])
    
    # 统计特征
    home_stats = current_stats[home_team]
    away_stats = current_stats[away_team]
    
    home_games = max(home_stats['games'], 1)
    away_games = max(away_stats['games'], 1)
    
    features.extend([
        # 积分相关
        home_stats['points'],
        away_stats['points'],
        home_stats['points'] - away_stats['points'],
        home_stats['points'] / (home_games * 3),
        away_stats['points'] / (away_games * 3),
        
        # 进球相关
        home_stats['goals_for'],
        away_stats['goals_for'],
        home_stats['goals_against'],
        away_stats['goals_against'],
        home_stats['goals_for'] - home_stats['goals_against'],
        away_stats['goals_for'] - away_stats['goals_against'],
        home_stats['goals_for'] / home_games,
        away_stats['goals_for'] / away_games,
        home_stats['goals_against'] / home_games,
        away_stats['goals_against'] / away_games,
        
        # 胜率相关
        home_stats['wins'] / home_games,
        away_stats['wins'] / away_games,
        home_stats['draws'] / home_games,
        away_stats['draws'] / away_games,
        home_stats['losses'] / home_games,
        away_stats['losses'] / away_games,
    ])
    
    # 主客场表现
    home_home_games = max(home_stats['home_games'], 1)
    away_away_games = max(away_stats['away_games'], 1)
    
    features.extend([
        home_stats['home_wins'] / home_home_games,
        away_stats['away_wins'] / away_away_games,
        home_stats['home_draws'] / home_home_games,
        away_stats['away_draws'] / away_away_games,
        home_stats['home_losses'] / home_home_games,
        away_stats['away_losses'] / away_away_games,
    ])
    
    # 特殊指标
    features.extend([
        home_stats['big_wins'],
        away_stats['big_wins'],
        home_stats['big_losses'],
        away_stats['big_losses'],
        home_stats['clean_sheets'],
        away_stats['clean_sheets'],
        home_stats['failed_to_score'],
        away_stats['failed_to_score'],
    ])
    
    # 最近状态
    features.extend([
        sum(home_stats['recent_form']) / len(home_stats['recent_form']),
        sum(away_stats['recent_form']) / len(away_stats['recent_form']),
    ])
    
    return features

def simulate_season_with_xgboost(predictions, current_stats, num_simulations=5000):
    """使用XGBoost预测结果进行蒙特卡洛模拟"""
    print(f"🎲 基于XGBoost预测进行 {num_simulations} 次蒙特卡洛模拟...")
    
    # 初始化当前积分
    current_points = {team: stats['points'] for team, stats in current_stats.items()}
    current_goal_diff = {team: stats['goals_for'] - stats['goals_against'] for team, stats in current_stats.items()}
    current_goals = {team: stats['goals_for'] for team, stats in current_stats.items()}
    
    final_rankings = []
    
    for sim in range(num_simulations):
        sim_points = current_points.copy()
        sim_goal_diff = current_goal_diff.copy()
        sim_goals = current_goals.copy()
        
        # 模拟剩余比赛
        for pred in predictions:
            home_team = pred['home_team']
            away_team = pred['away_team']
            
            # 使用XGBoost预测的概率进行随机采样
            rand = random.random()
            
            if rand < pred['prob_home_win']:
                # 主胜
                sim_points[home_team] += 3
                goal_diff = random.choice([1, 2, 3])
                sim_goal_diff[home_team] += goal_diff
                sim_goal_diff[away_team] -= goal_diff
                sim_goals[home_team] += goal_diff + random.choice([0, 1])
            elif rand < pred['prob_home_win'] + pred['prob_draw']:
                # 平局
                sim_points[home_team] += 1
                sim_points[away_team] += 1
                goals = random.choice([0, 1, 2])
                sim_goals[home_team] += goals
                sim_goals[away_team] += goals
            else:
                # 客胜
                sim_points[away_team] += 3
                goal_diff = random.choice([1, 2, 3])
                sim_goal_diff[away_team] += goal_diff
                sim_goal_diff[home_team] -= goal_diff
                sim_goals[away_team] += goal_diff + random.choice([0, 1])
        
        # 计算最终排名
        teams_final = []
        for team in sim_points.keys():
            teams_final.append((team, sim_points[team], sim_goal_diff[team], sim_goals[team]))
        
        teams_final.sort(key=lambda x: (x[1], x[2], x[3]), reverse=True)
        ranking = [team[0] for team in teams_final]
        final_rankings.append(ranking)
        
        if (sim + 1) % 1000 == 0:
            print(f"  完成 {sim + 1} 次模拟...")
    
    return final_rankings

def analyze_xgboost_results(final_rankings):
    """分析XGBoost预测结果"""
    from collections import defaultdict
    
    position_probs = defaultdict(lambda: defaultdict(int))
    
    for ranking in final_rankings:
        for pos, team in enumerate(ranking, 1):
            position_probs[team][pos] += 1
    
    # 转换为概率
    num_sims = len(final_rankings)
    for team in position_probs:
        for pos in position_probs[team]:
            position_probs[team][pos] = position_probs[team][pos] / num_sims
    
    return position_probs

def print_xgboost_predictions(predictions, position_probs):
    """打印XGBoost预测结果"""
    print("\n" + "="*60)
    print("🤖 基于XGBoost的足球联赛预测结果")
    print("="*60)
    
    # 剩余比赛预测
    print("\n🔮 剩余比赛预测 (TOP 10):")
    print("轮次        主队    vs 客队    预测结果  置信度   主胜率  平局率  客胜率")
    print("-" * 70)
    
    for i, pred in enumerate(predictions[:10]):
        print(f"{pred['round']:8s} {pred['home_team']:6s} vs {pred['away_team']:6s}  {pred['predicted_result']:4s}    {pred['confidence']:.1%}   {pred['prob_home_win']:.1%}   {pred['prob_draw']:.1%}   {pred['prob_away_win']:.1%}")
    
    if len(predictions) > 10:
        print(f"... 还有 {len(predictions) - 10} 场比赛")
    
    # 最终排名预测
    print(f"\n🏆 最终排名预测:")
    expected_rankings = []
    for team in position_probs:
        expected_rank = sum(pos * prob for pos, prob in position_probs[team].items())
        expected_rankings.append((team, expected_rank))
    
    expected_rankings.sort(key=lambda x: x[1])
    
    print("排名  队伍      期望排名  前三概率  保级概率")
    print("-" * 45)
    
    for i, (team, exp_rank) in enumerate(expected_rankings, 1):
        top3_prob = sum(position_probs[team].get(pos, 0) for pos in [1, 2, 3])
        safe_prob = sum(position_probs[team].get(pos, 0) for pos in range(1, 11))
        
        print(f"{i:2d}   {team:8s}  {exp_rank:6.1f}    {top3_prob:6.1%}    {safe_prob:6.1%}")

def main():
    """主函数"""
    print("🚀 XGBoost足球预测模型评估")
    print("="*40)
    
    # 预测剩余比赛
    predictions = predict_remaining_matches()
    
    # 加载当前统计数据
    df_stats = pd.read_csv('team_stats_for_prediction_updated.csv', encoding='utf-8')
    current_stats = {}
    for _, row in df_stats.iterrows():
        team = row['队伍名称']
        current_stats[team] = {
            'points': row['积分'],
            'goals_for': row['进球数'],
            'goals_against': row['失球数']
        }
    
    # 蒙特卡洛模拟
    final_rankings = simulate_season_with_xgboost(predictions, current_stats)
    
    # 分析结果
    position_probs = analyze_xgboost_results(final_rankings)
    
    # 打印结果
    print_xgboost_predictions(predictions, position_probs)
    
    print(f"\n💡 XGBoost模型特点:")
    print(f"✅ 强正则化防止过拟合")
    print(f"✅ 55场数据一视同仁训练")
    print(f"✅ 41维特征包含ELO评分")
    print(f"✅ 预测主场队伍胜/平/负")

if __name__ == "__main__":
    main()
