#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版贝叶斯模型实现
使用scipy.optimize和数值方法实现BTD和泊松模型
"""

import pandas as pd
import numpy as np
from scipy.optimize import minimize
from scipy import stats
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

class SimplifiedBTDModel:
    """简化的Bradley-Terry-Davidson模型"""
    
    def __init__(self):
        self.teams = []
        self.team_to_idx = {}
        self.alpha = None  # 队伍强度参数
        self.gamma = None  # 平局参数
        self.home_advantage = None
        
    def load_data(self):
        """加载数据"""
        print("📊 加载比赛数据...")
        
        df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
        
        matches = []
        teams = set()
        
        for _, row in df_results.iterrows():
            if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
                goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
                goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
                
                if pd.notna(row[goals1_col]) and pd.notna(row[goals2_col]):
                    home_team = row['参赛队伍1']
                    away_team = row['参赛队伍2']
                    home_goals = int(row[goals1_col])
                    away_goals = int(row[goals2_col])
                    
                    teams.add(home_team)
                    teams.add(away_team)
                    
                    if home_goals > away_goals:
                        result = 1  # 主胜
                    elif home_goals < away_goals:
                        result = -1  # 主负
                    else:
                        result = 0  # 平局
                    
                    matches.append({
                        'home_team': home_team,
                        'away_team': away_team,
                        'result': result
                    })
        
        self.teams = sorted(list(teams))
        self.team_to_idx = {team: i for i, team in enumerate(self.teams)}
        
        print(f"队伍数量: {len(self.teams)}")
        print(f"比赛数量: {len(matches)}")
        
        return matches
    
    def calculate_initial_strengths(self, matches):
        """计算初始强度估计"""
        team_stats = {team: {'wins': 0, 'draws': 0, 'losses': 0} for team in self.teams}
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            result = match['result']
            
            if result == 1:  # 主胜
                team_stats[home_team]['wins'] += 1
                team_stats[away_team]['losses'] += 1
            elif result == -1:  # 主负
                team_stats[away_team]['wins'] += 1
                team_stats[home_team]['losses'] += 1
            else:  # 平局
                team_stats[home_team]['draws'] += 1
                team_stats[away_team]['draws'] += 1
        
        # 计算胜率作为初始强度
        initial_strengths = []
        for team in self.teams:
            stats = team_stats[team]
            total_games = stats['wins'] + stats['draws'] + stats['losses']
            if total_games > 0:
                win_rate = (stats['wins'] + 0.5 * stats['draws']) / total_games
                strength = max(win_rate * 2 + 0.1, 0.1)  # 映射到[0.1, 2.1]
            else:
                strength = 1.0
            initial_strengths.append(strength)
        
        return np.array(initial_strengths)
    
    def btd_likelihood(self, params, matches):
        """BTD模型的负对数似然函数"""
        n_teams = len(self.teams)
        
        # 参数解包
        log_alpha = params[:n_teams]
        log_gamma = params[n_teams]
        home_adv = params[n_teams + 1]
        
        alpha = np.exp(log_alpha)
        gamma = np.exp(log_gamma)
        
        neg_log_likelihood = 0
        
        for match in matches:
            home_idx = self.team_to_idx[match['home_team']]
            away_idx = self.team_to_idx[match['away_team']]
            result = match['result']
            
            # 计算概率
            alpha_home = alpha[home_idx] * np.exp(home_adv)
            alpha_away = alpha[away_idx]
            total = alpha_home + alpha_away + gamma
            
            p_home_win = alpha_home / total
            p_draw = gamma / total
            p_away_win = alpha_away / total
            
            # 添加到似然
            if result == 1:  # 主胜
                neg_log_likelihood -= np.log(max(p_home_win, 1e-10))
            elif result == 0:  # 平局
                neg_log_likelihood -= np.log(max(p_draw, 1e-10))
            else:  # 主负
                neg_log_likelihood -= np.log(max(p_away_win, 1e-10))
        
        # 添加正则化项 (简化的先验)
        reg_alpha = 0.01 * np.sum((log_alpha - np.log(1.0))**2)  # 强度参数的L2正则化
        reg_gamma = 0.01 * (log_gamma - np.log(0.5))**2  # 平局参数的正则化
        reg_home = 0.01 * (home_adv - 0.1)**2  # 主场优势的正则化
        
        return neg_log_likelihood + reg_alpha + reg_gamma + reg_home
    
    def fit_btd_model(self, matches):
        """拟合BTD模型"""
        print("🔥 拟合BTD模型...")
        
        initial_strengths = self.calculate_initial_strengths(matches)
        n_teams = len(self.teams)
        
        # 初始参数
        initial_params = np.concatenate([
            np.log(initial_strengths),  # log(alpha)
            [np.log(0.5)],             # log(gamma)
            [0.1]                      # home_advantage
        ])
        
        # 优化
        result = minimize(
            self.btd_likelihood,
            initial_params,
            args=(matches,),
            method='L-BFGS-B',
            options={'maxiter': 1000}
        )
        
        if result.success:
            # 提取参数
            log_alpha = result.x[:n_teams]
            log_gamma = result.x[n_teams]
            home_adv = result.x[n_teams + 1]
            
            self.alpha = np.exp(log_alpha)
            self.gamma = np.exp(log_gamma)
            self.home_advantage = home_adv
            
            print(f"✅ BTD模型拟合成功")
            print(f"平局参数 γ: {self.gamma:.3f}")
            print(f"主场优势: {self.home_advantage:.3f}")
        else:
            print(f"❌ BTD模型拟合失败: {result.message}")
            
        return result.success
    
    def predict_btd(self, home_team, away_team):
        """BTD预测"""
        if self.alpha is None:
            raise ValueError("模型尚未训练")
        
        home_idx = self.team_to_idx[home_team]
        away_idx = self.team_to_idx[away_team]
        
        alpha_home = self.alpha[home_idx] * np.exp(self.home_advantage)
        alpha_away = self.alpha[away_idx]
        total = alpha_home + alpha_away + self.gamma
        
        p_home_win = alpha_home / total
        p_draw = self.gamma / total
        p_away_win = alpha_away / total
        
        return {
            'p_home_win': p_home_win,
            'p_draw': p_draw,
            'p_away_win': p_away_win,
            'predicted_result': np.argmax([p_away_win, p_draw, p_home_win])
        }
    
    def evaluate_btd(self, matches):
        """评估BTD模型"""
        print("📊 评估BTD模型...")
        
        predictions = []
        actuals = []
        
        for match in matches:
            pred = self.predict_btd(match['home_team'], match['away_team'])
            predictions.append(pred['predicted_result'])
            
            result = match['result']
            if result == 1:
                actuals.append(2)  # 主胜
            elif result == -1:
                actuals.append(0)  # 主负
            else:
                actuals.append(1)  # 平局
        
        accuracy = accuracy_score(actuals, predictions)
        print(f"BTD准确率: {accuracy:.3f}")
        
        print("\nBTD分类报告:")
        print(classification_report(actuals, predictions, target_names=['客胜', '平局', '主胜']))
        
        return accuracy

class SimplifiedPoissonModel:
    """简化的泊松回归模型"""
    
    def __init__(self):
        self.teams = []
        self.team_to_idx = {}
        self.attack = None
        self.defense = None
        self.home_advantage = None
        
    def load_data(self):
        """加载数据"""
        print("📊 加载比赛数据...")
        
        df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
        
        matches = []
        teams = set()
        
        for _, row in df_results.iterrows():
            if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
                goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
                goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
                
                if pd.notna(row[goals1_col]) and pd.notna(row[goals2_col]):
                    home_team = row['参赛队伍1']
                    away_team = row['参赛队伍2']
                    home_goals = int(row[goals1_col])
                    away_goals = int(row[goals2_col])
                    
                    teams.add(home_team)
                    teams.add(away_team)
                    
                    matches.append({
                        'home_team': home_team,
                        'away_team': away_team,
                        'home_goals': home_goals,
                        'away_goals': away_goals
                    })
        
        self.teams = sorted(list(teams))
        self.team_to_idx = {team: i for i, team in enumerate(self.teams)}
        
        print(f"队伍数量: {len(self.teams)}")
        print(f"比赛数量: {len(matches)}")
        
        return matches
    
    def poisson_likelihood(self, params, matches):
        """泊松模型的负对数似然函数"""
        n_teams = len(self.teams)
        
        # 参数解包
        log_attack = params[:n_teams]
        log_defense = params[n_teams:2*n_teams]
        home_adv = params[2*n_teams]
        
        attack = np.exp(log_attack)
        defense = np.exp(log_defense)
        
        neg_log_likelihood = 0
        
        for match in matches:
            home_idx = self.team_to_idx[match['home_team']]
            away_idx = self.team_to_idx[match['away_team']]
            home_goals = match['home_goals']
            away_goals = match['away_goals']
            
            # 计算期望进球数
            lambda_home = attack[home_idx] * defense[away_idx] * np.exp(home_adv)
            lambda_away = attack[away_idx] * defense[home_idx]
            
            # 泊松似然
            neg_log_likelihood -= stats.poisson.logpmf(home_goals, lambda_home)
            neg_log_likelihood -= stats.poisson.logpmf(away_goals, lambda_away)
        
        # 正则化项
        reg_attack = 0.01 * np.sum((log_attack - np.log(1.0))**2)
        reg_defense = 0.01 * np.sum((log_defense - np.log(1.0))**2)
        reg_home = 0.01 * (home_adv - 0.2)**2
        
        return neg_log_likelihood + reg_attack + reg_defense + reg_home
    
    def fit_poisson_model(self, matches):
        """拟合泊松模型"""
        print("🔥 拟合泊松模型...")
        
        n_teams = len(self.teams)
        
        # 初始参数估计
        team_stats = {team: {'goals_for': 0, 'goals_against': 0, 'games': 0} for team in self.teams}
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            home_goals = match['home_goals']
            away_goals = match['away_goals']
            
            team_stats[home_team]['goals_for'] += home_goals
            team_stats[home_team]['goals_against'] += away_goals
            team_stats[home_team]['games'] += 1
            
            team_stats[away_team]['goals_for'] += away_goals
            team_stats[away_team]['goals_against'] += home_goals
            team_stats[away_team]['games'] += 1
        
        initial_attack = []
        initial_defense = []
        
        for team in self.teams:
            stats = team_stats[team]
            if stats['games'] > 0:
                attack_rate = max(stats['goals_for'] / stats['games'], 0.1)
                defense_rate = max(stats['goals_against'] / stats['games'], 0.1)
            else:
                attack_rate = 1.0
                defense_rate = 1.0
            
            initial_attack.append(attack_rate)
            initial_defense.append(defense_rate)
        
        # 初始参数
        initial_params = np.concatenate([
            np.log(initial_attack),
            np.log(initial_defense),
            [0.2]  # home_advantage
        ])
        
        # 优化
        result = minimize(
            self.poisson_likelihood,
            initial_params,
            args=(matches,),
            method='L-BFGS-B',
            options={'maxiter': 1000}
        )
        
        if result.success:
            log_attack = result.x[:n_teams]
            log_defense = result.x[n_teams:2*n_teams]
            home_adv = result.x[2*n_teams]
            
            self.attack = np.exp(log_attack)
            self.defense = np.exp(log_defense)
            self.home_advantage = home_adv
            
            print(f"✅ 泊松模型拟合成功")
            print(f"主场优势: {self.home_advantage:.3f}")
        else:
            print(f"❌ 泊松模型拟合失败: {result.message}")
            
        return result.success
    
    def predict_poisson(self, home_team, away_team, n_simulations=1000):
        """泊松预测"""
        if self.attack is None:
            raise ValueError("模型尚未训练")
        
        home_idx = self.team_to_idx[home_team]
        away_idx = self.team_to_idx[away_team]
        
        lambda_home = self.attack[home_idx] * self.defense[away_idx] * np.exp(self.home_advantage)
        lambda_away = self.attack[away_idx] * self.defense[home_idx]
        
        # 蒙特卡洛模拟
        home_wins = 0
        draws = 0
        away_wins = 0
        
        for _ in range(n_simulations):
            home_goals = np.random.poisson(lambda_home)
            away_goals = np.random.poisson(lambda_away)
            
            if home_goals > away_goals:
                home_wins += 1
            elif home_goals < away_goals:
                away_wins += 1
            else:
                draws += 1
        
        p_home_win = home_wins / n_simulations
        p_draw = draws / n_simulations
        p_away_win = away_wins / n_simulations
        
        return {
            'expected_home_goals': lambda_home,
            'expected_away_goals': lambda_away,
            'p_home_win': p_home_win,
            'p_draw': p_draw,
            'p_away_win': p_away_win,
            'predicted_result': np.argmax([p_away_win, p_draw, p_home_win])
        }
    
    def evaluate_poisson(self, matches):
        """评估泊松模型"""
        print("📊 评估泊松模型...")
        
        predictions = []
        actuals = []
        
        for match in matches:
            pred = self.predict_poisson(match['home_team'], match['away_team'])
            predictions.append(pred['predicted_result'])
            
            home_goals = match['home_goals']
            away_goals = match['away_goals']
            
            if home_goals > away_goals:
                actuals.append(2)  # 主胜
            elif home_goals < away_goals:
                actuals.append(0)  # 主负
            else:
                actuals.append(1)  # 平局
        
        accuracy = accuracy_score(actuals, predictions)
        print(f"泊松准确率: {accuracy:.3f}")
        
        print("\n泊松分类报告:")
        print(classification_report(actuals, predictions, target_names=['客胜', '平局', '主胜']))
        
        return accuracy

def main():
    """主函数"""
    print("🎯 贝叶斯模型对比分析")
    print("="*50)
    
    # BTD模型
    print("\n1️⃣ Bradley-Terry-Davidson模型")
    print("-" * 30)
    btd_model = SimplifiedBTDModel()
    matches_btd = btd_model.load_data()
    
    if btd_model.fit_btd_model(matches_btd):
        btd_accuracy = btd_model.evaluate_btd(matches_btd)
        
        # 显示队伍强度
        print("\n🏆 BTD队伍强度排名:")
        team_strengths = [(team, btd_model.alpha[i]) for i, team in enumerate(btd_model.teams)]
        team_strengths.sort(key=lambda x: x[1], reverse=True)
        
        for i, (team, strength) in enumerate(team_strengths[:8], 1):
            print(f"  {i:2d}. {team:8s}: {strength:.3f}")
    
    # 泊松模型
    print("\n2️⃣ 分层泊松回归模型")
    print("-" * 30)
    poisson_model = SimplifiedPoissonModel()
    matches_poisson = poisson_model.load_data()
    
    if poisson_model.fit_poisson_model(matches_poisson):
        poisson_accuracy = poisson_model.evaluate_poisson(matches_poisson)
        
        # 显示队伍攻防能力
        print("\n🏆 泊松队伍攻防能力:")
        team_abilities = []
        for i, team in enumerate(poisson_model.teams):
            attack = poisson_model.attack[i]
            defense = poisson_model.defense[i]
            overall = attack / defense
            team_abilities.append((team, attack, defense, overall))
        
        team_abilities.sort(key=lambda x: x[3], reverse=True)
        
        print("排名  队伍      攻击力  防守力  攻防比")
        print("-" * 40)
        for i, (team, att, def_, overall) in enumerate(team_abilities[:8], 1):
            print(f"{i:2d}   {team:8s}  {att:.3f}  {def_:.3f}  {overall:.3f}")
    
    print(f"\n📊 模型对比总结:")
    print(f"BTD模型准确率: {btd_accuracy:.1%}")
    print(f"泊松模型准确率: {poisson_accuracy:.1%}")
    
    print(f"\n💡 贝叶斯方法优势:")
    print(f"✅ 专门处理小样本问题")
    print(f"✅ 通过先验信息弥补数据不足")
    print(f"✅ BTD模型直接建模平局概率")
    print(f"✅ 泊松模型考虑攻防交互")

if __name__ == "__main__":
    main()
