#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三种预测方法的综合对比分析
1. 原始权重方法
2. ELO评分方法  
3. 机器学习优化方法
"""

import pandas as pd
import numpy as np

def compare_all_methods():
    """对比所有三种方法的预测结果"""
    
    print("🔬 三种预测方法综合对比分析")
    print("="*60)
    
    # 三种方法的预测结果 (基于之前的运行结果)
    methods_results = {
        "原始权重法": {
            "南通队": 1.0, "南京队": 2.7, "盐城队": 2.8, "徐州队": 3.6, "泰州队": 5.1,
            "苏州队": 6.7, "无锡队": 7.1, "连云港队": 8.9, "镇江队": 8.9, "宿迁队": 9.7,
            "淮安队": 10.3, "扬州队": 11.4, "常州队": 12.8
        },
        "ELO评分法": {
            "南通队": 1.0, "南京队": 2.6, "盐城队": 3.2, "徐州队": 3.8, "泰州队": 5.2,
            "苏州队": 6.8, "无锡队": 7.0, "连云港队": 8.7, "镇江队": 9.1, "淮安队": 9.8,
            "宿迁队": 10.2, "扬州队": 11.6, "常州队": 12.9
        },
        "机器学习法": {
            "南通队": 1.0, "南京队": 2.7, "盐城队": 3.1, "徐州队": 3.4, "泰州队": 5.8,
            "苏州队": 6.9, "无锡队": 6.9, "连云港队": 8.4, "镇江队": 9.6, "宿迁队": 10.1,
            "淮安队": 10.3, "扬州队": 10.7, "常州队": 12.0
        }
    }
    
    # 前三概率对比
    top3_probs = {
        "原始权重法": {
            "南通队": 100.0, "南京队": 81.2, "盐城队": 84.8, "徐州队": 32.8, "泰州队": 1.1
        },
        "ELO评分法": {
            "南通队": 98.5, "南京队": 83.0, "盐城队": 54.2, "徐州队": 46.8, "泰州队": 0.8
        },
        "机器学习法": {
            "南通队": 100.0, "南京队": 79.8, "盐城队": 69.5, "徐州队": 47.9, "泰州队": 1.4
        }
    }
    
    # 降级概率对比 (倒数三名)
    relegation_probs = {
        "原始权重法": {
            "常州队": 88.2, "扬州队": 79.5, "淮安队": 53.7, "宿迁队": 36.6, "镇江队": 15.6
        },
        "ELO评分法": {
            "常州队": 98.0, "扬州队": 66.9, "淮安队": 45.2, "宿迁队": 38.1, "镇江队": 18.3
        },
        "机器学习法": {
            "常州队": 87.1, "扬州队": 60.0, "淮安队": 50.6, "宿迁队": 43.4, "镇江队": 34.7
        }
    }
    
    print_comparison_table(methods_results)
    print_probability_comparison(top3_probs, relegation_probs)
    analyze_method_differences(methods_results)
    print_method_characteristics()

def print_comparison_table(methods_results):
    """打印排名对比表"""
    print("\n📊 期望排名对比表")
    print("-" * 70)
    print("队伍        原始权重法  ELO评分法  机器学习法  标准差   一致性")
    print("-" * 70)
    
    all_teams = list(methods_results["原始权重法"].keys())
    
    for team in all_teams:
        ranks = [
            methods_results["原始权重法"][team],
            methods_results["ELO评分法"][team], 
            methods_results["机器学习法"][team]
        ]
        
        std_dev = np.std(ranks)
        consistency = "高" if std_dev < 0.5 else "中" if std_dev < 1.0 else "低"
        
        print(f"{team:8s}    {ranks[0]:6.1f}      {ranks[1]:6.1f}     {ranks[2]:6.1f}     {std_dev:5.2f}   {consistency}")

def print_probability_comparison(top3_probs, relegation_probs):
    """打印概率对比"""
    print("\n🏆 前三概率对比 (%)")
    print("-" * 60)
    print("队伍        原始权重法  ELO评分法  机器学习法")
    print("-" * 60)
    
    for team in ["南通队", "南京队", "盐城队", "徐州队", "泰州队"]:
        p1 = top3_probs["原始权重法"].get(team, 0)
        p2 = top3_probs["ELO评分法"].get(team, 0)
        p3 = top3_probs["机器学习法"].get(team, 0)
        print(f"{team:8s}    {p1:6.1f}      {p2:6.1f}     {p3:6.1f}")
    
    print("\n⚠️  降级概率对比 (%)")
    print("-" * 60)
    print("队伍        原始权重法  ELO评分法  机器学习法")
    print("-" * 60)
    
    for team in ["常州队", "扬州队", "淮安队", "宿迁队", "镇江队"]:
        p1 = relegation_probs["原始权重法"].get(team, 0)
        p2 = relegation_probs["ELO评分法"].get(team, 0)
        p3 = relegation_probs["机器学习法"].get(team, 0)
        print(f"{team:8s}    {p1:6.1f}      {p2:6.1f}     {p3:6.1f}")

def analyze_method_differences(methods_results):
    """分析方法差异"""
    print("\n🔍 方法差异分析")
    print("="*50)
    
    # 计算方法间的相关性
    teams = list(methods_results["原始权重法"].keys())
    
    original_ranks = [methods_results["原始权重法"][team] for team in teams]
    elo_ranks = [methods_results["ELO评分法"][team] for team in teams]
    ml_ranks = [methods_results["机器学习法"][team] for team in teams]
    
    # 计算相关系数
    corr_orig_elo = np.corrcoef(original_ranks, elo_ranks)[0, 1]
    corr_orig_ml = np.corrcoef(original_ranks, ml_ranks)[0, 1]
    corr_elo_ml = np.corrcoef(elo_ranks, ml_ranks)[0, 1]
    
    print(f"\n📈 方法间相关性:")
    print(f"  原始权重法 vs ELO评分法:   {corr_orig_elo:.3f}")
    print(f"  原始权重法 vs 机器学习法:   {corr_orig_ml:.3f}")
    print(f"  ELO评分法 vs 机器学习法:    {corr_elo_ml:.3f}")
    
    # 找出差异最大的预测
    print(f"\n🎯 差异最大的预测:")
    max_diff_teams = []
    
    for team in teams:
        ranks = [
            methods_results["原始权重法"][team],
            methods_results["ELO评分法"][team],
            methods_results["机器学习法"][team]
        ]
        diff = max(ranks) - min(ranks)
        max_diff_teams.append((team, diff, ranks))
    
    max_diff_teams.sort(key=lambda x: x[1], reverse=True)
    
    for i, (team, diff, ranks) in enumerate(max_diff_teams[:5], 1):
        print(f"  {i}. {team}: 差异{diff:.1f} (排名范围: {min(ranks):.1f}-{max(ranks):.1f})")

def print_method_characteristics():
    """打印各方法特点"""
    print("\n💡 各方法特点总结")
    print("="*50)
    
    print("\n🎯 原始权重法:")
    print("  ✅ 优势: 直观易懂，考虑多维度指标")
    print("  ❌ 劣势: 权重设置主观，可能存在偏差")
    print("  🎲 适用: 快速预测，权重经验丰富时")
    
    print("\n🎯 ELO评分法:")
    print("  ✅ 优势: 完全客观，理论成熟，动态更新")
    print("  ❌ 劣势: 过度依赖历史，对新情况适应慢")
    print("  🎲 适用: 长期跟踪，历史数据充足时")
    
    print("\n🎯 机器学习法:")
    print("  ✅ 优势: 数据驱动，自动优化权重，特征丰富")
    print("  ❌ 劣势: 需要足够训练数据，模型复杂度高")
    print("  🎲 适用: 数据充足，追求最优预测精度时")

def generate_final_recommendation():
    """生成最终推荐"""
    print("\n🏆 最终推荐方案")
    print("="*50)
    
    print("\n🥇 推荐策略: 集成预测法")
    print("  1. 主要方法: 机器学习法 (权重50%)")
    print("     - 数据驱动，自动优化")
    print("     - 考虑最全面的特征")
    
    print("  2. 验证方法: ELO评分法 (权重30%)")
    print("     - 完全客观，理论成熟")
    print("     - 提供独立验证")
    
    print("  3. 参考方法: 原始权重法 (权重20%)")
    print("     - 直观易懂，经验支撑")
    print("     - 作为常识性检验")
    
    print("\n📊 集成预测排名 (加权平均):")
    
    # 简化的集成计算
    integrated_ranks = {
        "南通队": 1.0, "南京队": 2.7, "盐城队": 3.0, "徐州队": 3.6, "泰州队": 5.5,
        "苏州队": 6.8, "无锡队": 7.0, "连云港队": 8.6, "镇江队": 9.4, "宿迁队": 10.0,
        "淮安队": 10.2, "扬州队": 11.0, "常州队": 12.3
    }
    
    sorted_teams = sorted(integrated_ranks.items(), key=lambda x: x[1])
    
    for i, (team, rank) in enumerate(sorted_teams, 1):
        confidence = "高" if rank <= 5 or rank >= 11 else "中"
        print(f"  {i:2d}. {team:8s} (期望排名: {rank:.1f}, 置信度: {confidence})")

def main():
    """主函数"""
    compare_all_methods()
    generate_final_recommendation()
    
    print("\n🎯 核心结论:")
    print("  • 三种方法高度一致，相关性>0.95")
    print("  • 南通队夺冠、常州队垫底几乎确定")
    print("  • 前三争夺和保级大战仍有悬念")
    print("  • 机器学习法提供最全面的特征考虑")
    print("  • 建议使用集成方法提高预测稳健性")

if __name__ == "__main__":
    main()
