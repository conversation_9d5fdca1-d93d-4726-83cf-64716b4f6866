#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳健的BTD超参数优化
解决MCMC收敛和优化失败问题
"""

import pandas as pd
import numpy as np
import pymc as pm
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

class RobustBTDOptimizer:
    """稳健的BTD超参数优化器"""
    
    def __init__(self):
        self.teams = []
        self.team_to_idx = {}
        self.best_hyperparams = None
        self.best_accuracy = 0
        
    def load_data(self):
        """加载数据"""
        print("📊 加载比赛数据...")
        
        # 尝试读取不同的文件名
        try:
            df_results = pd.read_csv('maches2.csv', encoding='utf-8')
            print("✅ 成功读取 maches2.csv")
        except:
            try:
                df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
                print("✅ 成功读取 合并后的比赛数据.csv")
            except Exception as e:
                print(f"❌ 文件读取失败: {e}")
                return []
        
        matches = []
        teams = set()
        
        # 检查列名
        print("数据列名:", df_results.columns.tolist())
        
        for _, row in df_results.iterrows():
            # 尝试不同的列名组合
            home_team_cols = ['参赛队伍1', 'home_team', '主队', 'Home Team']
            away_team_cols = ['参赛队伍2', 'away_team', '客队', 'Away Team']
            home_goals_cols = ['参赛队伍1进球数', '参赛队伍1进球 数', 'home_goals', '主队进球', 'Home Goals']
            away_goals_cols = ['参赛队伍2进球数', 'away_goals', '客队进球', 'Away Goals']
            
            home_team = None
            away_team = None
            home_goals = None
            away_goals = None
            
            # 查找主队
            for col in home_team_cols:
                if col in row and pd.notna(row[col]):
                    home_team = row[col]
                    break
            
            # 查找客队
            for col in away_team_cols:
                if col in row and pd.notna(row[col]):
                    away_team = row[col]
                    break
            
            # 查找主队进球
            for col in home_goals_cols:
                if col in row and pd.notna(row[col]):
                    home_goals = int(row[col])
                    break
            
            # 查找客队进球
            for col in away_goals_cols:
                if col in row and pd.notna(row[col]):
                    away_goals = int(row[col])
                    break
            
            if all(x is not None for x in [home_team, away_team, home_goals, away_goals]):
                teams.add(home_team)
                teams.add(away_team)
                
                if home_goals > away_goals:
                    result = 1  # 主胜
                elif home_goals < away_goals:
                    result = -1  # 主负
                else:
                    result = 0  # 平局
                
                matches.append({
                    'home_team': home_team,
                    'away_team': away_team,
                    'result': result
                })
        
        self.teams = sorted(list(teams))
        self.team_to_idx = {team: i for i, team in enumerate(self.teams)}
        
        print(f"队伍数量: {len(self.teams)}")
        print(f"比赛数量: {len(matches)}")
        
        return matches
    
    def calculate_prior_strengths(self, matches):
        """计算先验强度"""
        team_stats = {team: {'wins': 0, 'draws': 0, 'losses': 0, 'games': 0} for team in self.teams}
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            result = match['result']
            
            team_stats[home_team]['games'] += 1
            team_stats[away_team]['games'] += 1
            
            if result == 1:  # 主胜
                team_stats[home_team]['wins'] += 1
                team_stats[away_team]['losses'] += 1
            elif result == -1:  # 主负
                team_stats[away_team]['wins'] += 1
                team_stats[home_team]['losses'] += 1
            else:  # 平局
                team_stats[home_team]['draws'] += 1
                team_stats[away_team]['draws'] += 1
        
        prior_strengths = []
        for team in self.teams:
            stats = team_stats[team]
            if stats['games'] > 0:
                win_rate = (stats['wins'] + 0.5 * stats['draws']) / stats['games']
                strength = max(win_rate * 2 + 0.1, 0.1)
            else:
                strength = 1.0
            prior_strengths.append(strength)
        
        return np.array(prior_strengths)
    
    def fit_btd_robust(self, matches, hyperparams):
        """稳健的BTD模型拟合"""
        prior_strengths = self.calculate_prior_strengths(matches)
        n_teams = len(self.teams)
        
        # 准备数据
        home_teams = np.array([self.team_to_idx[m['home_team']] for m in matches])
        away_teams = np.array([self.team_to_idx[m['away_team']] for m in matches])
        results = np.array([m['result'] for m in matches])
        
        try:
            with pm.Model() as model:
                # 队伍强度参数 - 使用更稳健的先验
                log_alpha = pm.Normal('log_alpha', 
                                    mu=np.log(prior_strengths), 
                                    sigma=hyperparams['alpha_sigma'],
                                    shape=n_teams)
                alpha = pm.Deterministic('alpha', pm.math.exp(log_alpha))
                
                # 平局参数 - 使用更稳健的先验
                gamma = pm.Gamma('gamma', 
                               alpha=hyperparams['gamma_alpha'], 
                               beta=hyperparams['gamma_beta'])
                
                # 主场优势 - 使用更稳健的先验
                home_advantage = pm.Normal('home_advantage', 
                                         mu=hyperparams['home_mu'], 
                                         sigma=hyperparams['home_sigma'])
                
                # BTD概率计算
                alpha_home = alpha[home_teams] * pm.math.exp(home_advantage)
                alpha_away = alpha[away_teams]
                total = alpha_home + alpha_away + gamma
                
                p_home_win = alpha_home / total
                p_draw = gamma / total
                p_away_win = alpha_away / total
                
                # 似然函数
                categorical_results = np.where(results == -1, 0, np.where(results == 0, 1, 2))
                probs_matrix = pm.math.stack([p_away_win, p_draw, p_home_win], axis=1)
                
                likelihood = pm.Categorical('likelihood', p=probs_matrix, observed=categorical_results)
            
            # 稳健的采样策略
            with model:
                # 使用更多链和更长的调优
                trace = pm.sample(
                    draws=hyperparams.get('n_samples', 800),
                    tune=hyperparams.get('n_tune', 600),
                    chains=4,  # 使用4条链确保收敛
                    cores=1,
                    random_seed=42,
                    return_inferencedata=True,
                    progressbar=False,
                    target_accept=0.9,  # 提高接受率
                    max_treedepth=12    # 增加树深度
                )
            
            # 检查收敛性
            rhat = pm.rhat(trace)
            max_rhat = float(np.max([np.max(rhat[var].values) for var in rhat.data_vars]))
            
            if max_rhat > 1.1:
                print(f"⚠️ 收敛性警告: max R-hat = {max_rhat:.3f}")
                return None
            
            return trace
            
        except Exception as e:
            print(f"❌ MCMC采样失败: {e}")
            return None
    
    def evaluate_btd(self, matches, trace):
        """评估BTD模型"""
        if trace is None:
            return 0.0
        
        try:
            predictions = []
            actuals = []
            
            for match in matches:
                pred = self.predict_btd(match['home_team'], match['away_team'], trace)
                if pred is None:
                    return 0.0
                predictions.append(pred['predicted_result'])
                
                result = match['result']
                if result == 1:
                    actuals.append(2)  # 主胜
                elif result == -1:
                    actuals.append(0)  # 主负
                else:
                    actuals.append(1)  # 平局
            
            accuracy = accuracy_score(actuals, predictions)
            return accuracy
            
        except Exception as e:
            print(f"❌ 评估失败: {e}")
            return 0.0
    
    def predict_btd(self, home_team, away_team, trace):
        """BTD预测"""
        try:
            home_idx = self.team_to_idx[home_team]
            away_idx = self.team_to_idx[away_team]
            
            # 从后验分布中采样
            alpha_samples = trace.posterior['alpha'].values.reshape(-1, len(self.teams))
            gamma_samples = trace.posterior['gamma'].values.flatten()
            home_adv_samples = trace.posterior['home_advantage'].values.flatten()
            
            # 计算预测概率
            alpha_home = alpha_samples[:, home_idx] * np.exp(home_adv_samples)
            alpha_away = alpha_samples[:, away_idx]
            total = alpha_home + alpha_away + gamma_samples
            
            p_home_win = np.mean(alpha_home / total)
            p_draw = np.mean(gamma_samples / total)
            p_away_win = np.mean(alpha_away / total)
            
            # 归一化
            total_prob = p_home_win + p_draw + p_away_win
            if total_prob > 0:
                p_home_win /= total_prob
                p_draw /= total_prob
                p_away_win /= total_prob
            else:
                return None
            
            return {
                'p_home_win': p_home_win,
                'p_draw': p_draw,
                'p_away_win': p_away_win,
                'predicted_result': np.argmax([p_away_win, p_draw, p_home_win])
            }
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            return None
    
    def grid_search_optimization(self, matches):
        """网格搜索优化（更稳健）"""
        print("🔍 进行网格搜索超参数优化...")
        
        # 定义搜索网格（较小的搜索空间）
        param_grid = {
            'alpha_sigma': [0.3, 0.5, 0.7],
            'gamma_alpha': [1.5, 2.0, 2.5],
            'gamma_beta': [1.5, 2.0, 2.5],
            'home_mu': [0.05, 0.1, 0.15],
            'home_sigma': [0.05, 0.1, 0.15]
        }
        
        best_accuracy = 0
        best_params = None
        total_combinations = np.prod([len(v) for v in param_grid.values()])
        
        print(f"总共需要测试 {total_combinations} 种参数组合")
        
        combination_count = 0
        
        for alpha_sigma in param_grid['alpha_sigma']:
            for gamma_alpha in param_grid['gamma_alpha']:
                for gamma_beta in param_grid['gamma_beta']:
                    for home_mu in param_grid['home_mu']:
                        for home_sigma in param_grid['home_sigma']:
                            combination_count += 1
                            
                            hyperparams = {
                                'alpha_sigma': alpha_sigma,
                                'gamma_alpha': gamma_alpha,
                                'gamma_beta': gamma_beta,
                                'home_mu': home_mu,
                                'home_sigma': home_sigma,
                                'n_samples': 600,
                                'n_tune': 400
                            }
                            
                            print(f"测试组合 {combination_count}/{total_combinations}: "
                                  f"α_σ={alpha_sigma}, γ_α={gamma_alpha}, γ_β={gamma_beta}, "
                                  f"h_μ={home_mu}, h_σ={home_sigma}")
                            
                            # 拟合模型
                            trace = self.fit_btd_robust(matches, hyperparams)
                            
                            if trace is not None:
                                # 评估性能
                                accuracy = self.evaluate_btd(matches, trace)
                                print(f"  准确率: {accuracy:.3f}")
                                
                                if accuracy > best_accuracy:
                                    best_accuracy = accuracy
                                    best_params = hyperparams.copy()
                                    print(f"  🎯 新的最佳准确率: {best_accuracy:.3f}")
                            else:
                                print(f"  ❌ 模型拟合失败")
        
        if best_params is not None:
            self.best_hyperparams = best_params
            self.best_accuracy = best_accuracy
            print(f"\n✅ 优化完成!")
            print(f"最佳准确率: {best_accuracy:.3f}")
            print("最佳参数:")
            for key, value in best_params.items():
                if key not in ['n_samples', 'n_tune']:
                    print(f"  {key}: {value}")
            return True
        else:
            print(f"\n❌ 优化失败: 没有找到有效的参数组合")
            return False
    
    def run_robust_optimization(self):
        """运行稳健优化"""
        print("🎯 稳健BTD超参数优化")
        print("="*50)
        
        # 1. 加载数据
        matches = self.load_data()
        if not matches:
            print("❌ 数据加载失败")
            return
        
        # 2. 测试基础模型
        print("\n📊 基础模型性能测试:")
        baseline_params = {
            'alpha_sigma': 0.5,
            'gamma_alpha': 2.0,
            'gamma_beta': 2.0,
            'home_mu': 0.1,
            'home_sigma': 0.1,
            'n_samples': 1000,
            'n_tune': 600
        }
        
        baseline_trace = self.fit_btd_robust(matches, baseline_params)
        if baseline_trace is not None:
            baseline_accuracy = self.evaluate_btd(matches, baseline_trace)
            print(f"基础模型准确率: {baseline_accuracy:.3f}")
        else:
            print("❌ 基础模型拟合失败")
            return
        
        # 3. 网格搜索优化
        success = self.grid_search_optimization(matches)
        
        if success:
            # 4. 最终模型评估
            print(f"\n📊 最优模型详细评估:")
            final_trace = self.fit_btd_robust(matches, self.best_hyperparams)
            
            if final_trace is not None:
                # 详细评估
                predictions = []
                actuals = []
                
                for match in matches:
                    pred = self.predict_btd(match['home_team'], match['away_team'], final_trace)
                    if pred is not None:
                        predictions.append(pred['predicted_result'])
                        
                        result = match['result']
                        if result == 1:
                            actuals.append(2)  # 主胜
                        elif result == -1:
                            actuals.append(0)  # 主负
                        else:
                            actuals.append(1)  # 平局
                
                if predictions:
                    final_accuracy = accuracy_score(actuals, predictions)
                    print(f"最终准确率: {final_accuracy:.3f}")
                    
                    print("\n分类报告:")
                    print(classification_report(actuals, predictions, 
                                              target_names=['客胜', '平局', '主胜']))
                    
                    improvement = final_accuracy - baseline_accuracy
                    print(f"\n📈 性能提升:")
                    print(f"准确率提升: {improvement:.3f} ({improvement/baseline_accuracy:.1%})")
        
        return success

def main():
    """主函数"""
    optimizer = RobustBTDOptimizer()
    success = optimizer.run_robust_optimization()
    
    if success:
        print(f"\n💡 优化成功!")
        print(f"✅ 解决了MCMC收敛问题")
        print(f"✅ 使用网格搜索替代梯度优化")
        print(f"✅ 增加了稳健性检查")
    else:
        print(f"\n💡 优化建议:")
        print(f"📊 数据可能需要更多样本")
        print(f"🔧 可以尝试更简单的模型")
        print(f"⚙️ 或者调整MCMC采样参数")

if __name__ == "__main__":
    main()
