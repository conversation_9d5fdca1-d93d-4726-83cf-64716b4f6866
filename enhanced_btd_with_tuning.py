#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版贝叶斯BTD模型
包含参数微调和特征融入
"""

import pandas as pd
import numpy as np
import pymc as pm
import arviz as az
from scipy.optimize import minimize
from sklearn.metrics import accuracy_score, classification_report, log_loss
import warnings
warnings.filterwarnings('ignore')

class EnhancedBTDModel:
    """增强版BTD模型，支持参数微调和特征融入"""
    
    def __init__(self, hyperparams=None):
        self.model = None
        self.trace = None
        self.teams = []
        self.team_to_idx = {}
        
        # 可调超参数
        self.hyperparams = hyperparams or {
            # 先验分布参数
            'alpha_prior_sigma': 0.5,      # 队伍强度先验的标准差
            'gamma_alpha': 2.0,            # 平局参数Gamma分布的alpha
            'gamma_beta': 2.0,             # 平局参数Gamma分布的beta
            'home_adv_mu': 0.1,            # 主场优势先验均值
            'home_adv_sigma': 0.1,         # 主场优势先验标准差
            
            # 特征权重先验
            'feature_weight_sigma': 0.3,   # 特征权重的先验标准差
            'form_decay': 0.8,             # 状态衰减因子
            'elo_k_factor': 32,            # ELO更新的K因子
            
            # 采样参数
            'n_samples': 1500,
            'n_tune': 1000,
            'n_chains': 2
        }
    
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("📊 加载比赛数据...")
        
        df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
        
        matches = []
        teams = set()
        
        for _, row in df_results.iterrows():
            if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
                goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
                goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
                
                if pd.notna(row[goals1_col]) and pd.notna(row[goals2_col]):
                    home_team = row['参赛队伍1']
                    away_team = row['参赛队伍2']
                    home_goals = int(row[goals1_col])
                    away_goals = int(row[goals2_col])
                    
                    teams.add(home_team)
                    teams.add(away_team)
                    
                    if home_goals > away_goals:
                        result = 1  # 主胜
                    elif home_goals < away_goals:
                        result = -1  # 主负
                    else:
                        result = 0  # 平局
                    
                    matches.append({
                        'round': row['轮次'],
                        'home_team': home_team,
                        'away_team': away_team,
                        'home_goals': home_goals,
                        'away_goals': away_goals,
                        'result': result
                    })
        
        self.teams = sorted(list(teams))
        self.team_to_idx = {team: i for i, team in enumerate(self.teams)}
        
        print(f"队伍数量: {len(self.teams)}")
        print(f"比赛数量: {len(matches)}")
        
        return matches
    
    def extract_enhanced_features(self, matches):
        """提取增强特征"""
        print("🔧 提取增强特征...")
        
        # 初始化ELO和累积统计
        elo_ratings = {team: 1500 for team in self.teams}
        team_stats = {}
        
        for team in self.teams:
            team_stats[team] = {
                'games': 0, 'wins': 0, 'draws': 0, 'losses': 0,
                'goals_for': 0, 'goals_against': 0, 'points': 0,
                'home_games': 0, 'home_wins': 0, 'away_games': 0, 'away_wins': 0,
                'recent_form': [], 'big_wins': 0, 'big_losses': 0,
                'clean_sheets': 0, 'failed_to_score': 0,
                'opponent_strength_sum': 0, 'opponent_count': 0,
                'goal_difference_trend': [], 'scoring_consistency': []
            }
        
        enhanced_matches = []
        
        for match in sorted(matches, key=lambda x: x['round']):
            home_team = match['home_team']
            away_team = match['away_team']
            home_goals = match['home_goals']
            away_goals = match['away_goals']
            result = match['result']
            
            # 提取比赛前的特征
            features = self.calculate_match_features(home_team, away_team, elo_ratings, team_stats)
            
            enhanced_match = match.copy()
            enhanced_match['features'] = features
            enhanced_matches.append(enhanced_match)
            
            # 更新统计数据
            self.update_team_stats(team_stats, elo_ratings, home_team, away_team, home_goals, away_goals)
        
        return enhanced_matches
    
    def calculate_match_features(self, home_team, away_team, elo_ratings, team_stats):
        """计算比赛特征"""
        home_stats = team_stats[home_team]
        away_stats = team_stats[away_team]
        
        features = {}
        
        # 1. ELO相关特征
        home_elo = elo_ratings[home_team]
        away_elo = elo_ratings[away_team]
        features['elo_diff'] = home_elo - away_elo
        features['elo_ratio'] = home_elo / (home_elo + away_elo)
        
        # 2. 基础实力特征
        home_games = max(home_stats['games'], 1)
        away_games = max(away_stats['games'], 1)
        
        features['points_diff'] = (home_stats['points'] / home_games) - (away_stats['points'] / away_games)
        features['goal_diff'] = ((home_stats['goals_for'] - home_stats['goals_against']) / home_games) - \
                               ((away_stats['goals_for'] - away_stats['goals_against']) / away_games)
        
        # 3. 攻防匹配特征
        home_attack = home_stats['goals_for'] / home_games if home_games > 0 else 1.0
        away_attack = away_stats['goals_for'] / away_games if away_games > 0 else 1.0
        home_defense = home_stats['goals_against'] / home_games if home_games > 0 else 1.0
        away_defense = away_stats['goals_against'] / away_games if away_games > 0 else 1.0
        
        features['attack_vs_defense'] = (home_attack / max(away_defense, 0.1)) - (away_attack / max(home_defense, 0.1))
        
        # 4. 主客场优势特征
        home_home_games = max(home_stats['home_games'], 1)
        away_away_games = max(away_stats['away_games'], 1)
        
        home_home_rate = home_stats['home_wins'] / home_home_games if home_home_games > 0 else 0
        away_away_rate = away_stats['away_wins'] / away_away_games if away_away_games > 0 else 0
        
        features['home_field_advantage'] = home_home_rate - away_away_rate
        
        # 5. 最近状态特征（带衰减）
        if home_stats['recent_form'] and away_stats['recent_form']:
            # 计算加权平均（最近的比赛权重更大）
            home_form = self.calculate_weighted_form(home_stats['recent_form'])
            away_form = self.calculate_weighted_form(away_stats['recent_form'])
            features['form_diff'] = home_form - away_form
        else:
            features['form_diff'] = 0
        
        # 6. 对手实力特征
        home_avg_opp = home_stats['opponent_strength_sum'] / max(home_stats['opponent_count'], 1)
        away_avg_opp = away_stats['opponent_strength_sum'] / max(away_stats['opponent_count'], 1)
        features['opponent_strength_diff'] = home_avg_opp - away_avg_opp
        
        # 7. 稳定性特征
        features['home_consistency'] = 1 / (1 + np.var(home_stats['scoring_consistency']) + 0.1) if home_stats['scoring_consistency'] else 0.5
        features['away_consistency'] = 1 / (1 + np.var(away_stats['scoring_consistency']) + 0.1) if away_stats['scoring_consistency'] else 0.5
        
        return features
    
    def calculate_weighted_form(self, form_list):
        """计算加权状态（最近的比赛权重更大）"""
        if not form_list:
            return 1.5  # 默认中等状态
        
        weights = [self.hyperparams['form_decay'] ** i for i in range(len(form_list))]
        weights.reverse()  # 最近的比赛权重最大
        
        weighted_sum = sum(f * w for f, w in zip(form_list, weights))
        weight_sum = sum(weights)
        
        return weighted_sum / weight_sum if weight_sum > 0 else 1.5
    
    def update_team_stats(self, team_stats, elo_ratings, home_team, away_team, home_goals, away_goals):
        """更新队伍统计"""
        # 更新ELO评分
        k_factor = self.hyperparams['elo_k_factor']
        
        if home_goals > away_goals:
            actual_home, actual_away = 1.0, 0.0
        elif home_goals < away_goals:
            actual_home, actual_away = 0.0, 1.0
        else:
            actual_home, actual_away = 0.5, 0.5
        
        expected_home = 1 / (1 + 10**((elo_ratings[away_team] - elo_ratings[home_team]) / 400))
        expected_away = 1 - expected_home
        
        elo_ratings[home_team] += k_factor * (actual_home - expected_home)
        elo_ratings[away_team] += k_factor * (actual_away - expected_away)
        
        # 更新统计数据
        for team, goals_for, goals_against, is_home in [
            (home_team, home_goals, away_goals, True),
            (away_team, away_goals, home_goals, False)
        ]:
            stats = team_stats[team]
            stats['games'] += 1
            stats['goals_for'] += goals_for
            stats['goals_against'] += goals_against
            
            if is_home:
                stats['home_games'] += 1
            else:
                stats['away_games'] += 1
            
            if goals_for > goals_against:
                stats['wins'] += 1
                stats['points'] += 3
                if is_home:
                    stats['home_wins'] += 1
                else:
                    stats['away_wins'] += 1
                stats['recent_form'].append(3)
                if goals_for - goals_against >= 2:
                    stats['big_wins'] += 1
            elif goals_for == goals_against:
                stats['draws'] += 1
                stats['points'] += 1
                stats['recent_form'].append(1)
            else:
                stats['losses'] += 1
                stats['recent_form'].append(0)
                if goals_against - goals_for >= 2:
                    stats['big_losses'] += 1
            
            # 保持最近5场记录
            if len(stats['recent_form']) > 5:
                stats['recent_form'] = stats['recent_form'][-5:]
            
            # 更新其他统计
            if goals_against == 0:
                stats['clean_sheets'] += 1
            if goals_for == 0:
                stats['failed_to_score'] += 1
            
            stats['goal_difference_trend'].append(goals_for - goals_against)
            stats['scoring_consistency'].append(goals_for)
            
            if len(stats['goal_difference_trend']) > 5:
                stats['goal_difference_trend'] = stats['goal_difference_trend'][-5:]
                stats['scoring_consistency'] = stats['scoring_consistency'][-5:]
        
        # 更新对手实力
        team_stats[home_team]['opponent_strength_sum'] += elo_ratings[away_team]
        team_stats[home_team]['opponent_count'] += 1
        team_stats[away_team]['opponent_strength_sum'] += elo_ratings[home_team]
        team_stats[away_team]['opponent_count'] += 1
    
    def build_enhanced_btd_model(self, enhanced_matches):
        """构建增强版BTD模型"""
        print("🏗️ 构建增强版BTD模型...")
        
        n_teams = len(self.teams)
        n_matches = len(enhanced_matches)
        
        # 准备数据
        home_teams = np.array([self.team_to_idx[m['home_team']] for m in enhanced_matches])
        away_teams = np.array([self.team_to_idx[m['away_team']] for m in enhanced_matches])
        results = np.array([m['result'] for m in enhanced_matches])
        
        # 提取特征矩阵
        feature_names = ['elo_diff', 'elo_ratio', 'points_diff', 'goal_diff', 'attack_vs_defense', 
                        'home_field_advantage', 'form_diff', 'opponent_strength_diff', 
                        'home_consistency', 'away_consistency']
        
        feature_matrix = np.array([[m['features'].get(fname, 0) for fname in feature_names] 
                                  for m in enhanced_matches])
        
        # 计算基础先验强度
        prior_strengths = self.calculate_prior_strengths(enhanced_matches)
        
        with pm.Model() as model:
            # 基础队伍强度参数
            log_alpha_base = pm.Normal('log_alpha_base', 
                                     mu=np.log(prior_strengths), 
                                     sigma=self.hyperparams['alpha_prior_sigma'],
                                     shape=n_teams)
            
            # 特征权重参数
            feature_weights = pm.Normal('feature_weights', 
                                      mu=0, 
                                      sigma=self.hyperparams['feature_weight_sigma'],
                                      shape=len(feature_names))
            
            # 平局参数
            gamma = pm.Gamma('gamma', 
                           alpha=self.hyperparams['gamma_alpha'], 
                           beta=self.hyperparams['gamma_beta'])
            
            # 基础主场优势
            home_advantage_base = pm.Normal('home_advantage_base', 
                                          mu=self.hyperparams['home_adv_mu'], 
                                          sigma=self.hyperparams['home_adv_sigma'])
            
            # 计算调整后的强度
            # 基础强度
            alpha_base = pm.math.exp(log_alpha_base)
            
            # 特征调整
            feature_effects = pm.math.dot(feature_matrix, feature_weights)
            
            # 主队和客队的特征效应
            home_feature_effects = feature_effects
            away_feature_effects = -feature_effects  # 客队获得相反的效应
            
            # 最终强度（基础强度 × 特征调整）
            alpha_home_adjusted = alpha_base[home_teams] * pm.math.exp(home_feature_effects)
            alpha_away_adjusted = alpha_base[away_teams] * pm.math.exp(away_feature_effects)
            
            # 主场优势调整
            alpha_home_final = alpha_home_adjusted * pm.math.exp(home_advantage_base)
            alpha_away_final = alpha_away_adjusted
            
            # BTD概率计算
            total = alpha_home_final + alpha_away_final + gamma
            
            p_home_win = alpha_home_final / total
            p_draw = gamma / total
            p_away_win = alpha_away_final / total
            
            # 似然函数
            categorical_results = np.where(results == -1, 0, np.where(results == 0, 1, 2))
            probs_matrix = pm.math.stack([p_away_win, p_draw, p_home_win], axis=1)
            
            likelihood = pm.Categorical('likelihood', p=probs_matrix, observed=categorical_results)
        
        self.model = model
        self.feature_names = feature_names
        return model
    
    def calculate_prior_strengths(self, enhanced_matches):
        """计算先验强度"""
        team_stats = {team: {'wins': 0, 'draws': 0, 'losses': 0, 'games': 0} for team in self.teams}
        
        for match in enhanced_matches:
            home_team = match['home_team']
            away_team = match['away_team']
            result = match['result']
            
            team_stats[home_team]['games'] += 1
            team_stats[away_team]['games'] += 1
            
            if result == 1:  # 主胜
                team_stats[home_team]['wins'] += 1
                team_stats[away_team]['losses'] += 1
            elif result == -1:  # 主负
                team_stats[away_team]['wins'] += 1
                team_stats[home_team]['losses'] += 1
            else:  # 平局
                team_stats[home_team]['draws'] += 1
                team_stats[away_team]['draws'] += 1
        
        prior_strengths = []
        for team in self.teams:
            stats = team_stats[team]
            if stats['games'] > 0:
                win_rate = (stats['wins'] + 0.5 * stats['draws']) / stats['games']
                strength = max(win_rate * 2 + 0.1, 0.1)
            else:
                strength = 1.0
            prior_strengths.append(strength)
        
        return np.array(prior_strengths)
    
    def fit_enhanced_model(self, enhanced_matches):
        """拟合增强模型"""
        print("🔥 拟合增强BTD模型...")
        
        model = self.build_enhanced_btd_model(enhanced_matches)
        
        with model:
            self.trace = pm.sample(
                draws=self.hyperparams['n_samples'],
                tune=self.hyperparams['n_tune'],
                chains=self.hyperparams['n_chains'],
                cores=1,
                random_seed=42,
                return_inferencedata=True,
                progressbar=True
            )
        
        print("✅ 增强模型拟合完成")
        return self.trace
    
    def hyperparameter_optimization(self, enhanced_matches, param_ranges):
        """超参数优化"""
        print("🎛️ 进行超参数优化...")
        
        def objective(params):
            # 更新超参数
            param_names = list(param_ranges.keys())
            for i, param_name in enumerate(param_names):
                self.hyperparams[param_name] = params[i]
            
            try:
                # 快速训练（减少采样数）
                original_samples = self.hyperparams['n_samples']
                original_tune = self.hyperparams['n_tune']
                
                self.hyperparams['n_samples'] = 500
                self.hyperparams['n_tune'] = 300
                
                # 拟合模型
                self.fit_enhanced_model(enhanced_matches)
                
                # 评估性能（使用训练集，快速评估）
                accuracy = self.evaluate_enhanced_model(enhanced_matches, verbose=False)
                
                # 恢复原始参数
                self.hyperparams['n_samples'] = original_samples
                self.hyperparams['n_tune'] = original_tune
                
                return -accuracy  # 最小化负准确率
                
            except Exception as e:
                print(f"优化过程中出错: {e}")
                return 1.0  # 返回最差分数
        
        # 设置优化边界
        bounds = [param_ranges[param] for param in param_ranges.keys()]
        initial_guess = [self.hyperparams[param] for param in param_ranges.keys()]
        
        # 优化
        result = minimize(objective, initial_guess, bounds=bounds, method='L-BFGS-B')
        
        if result.success:
            # 更新最优参数
            param_names = list(param_ranges.keys())
            for i, param_name in enumerate(param_names):
                self.hyperparams[param_name] = result.x[i]
            
            print(f"✅ 超参数优化完成，最优准确率: {-result.fun:.3f}")
            print("最优参数:")
            for param_name, value in zip(param_names, result.x):
                print(f"  {param_name}: {value:.3f}")
        else:
            print(f"❌ 超参数优化失败: {result.message}")
        
        return result.success
    
    def evaluate_enhanced_model(self, enhanced_matches, verbose=True):
        """评估增强模型"""
        if verbose:
            print("📊 评估增强模型...")
        
        predictions = []
        actuals = []
        
        for match in enhanced_matches:
            pred = self.predict_enhanced_match(match['home_team'], match['away_team'], match['features'])
            predictions.append(pred['predicted_result'])
            
            result = match['result']
            if result == 1:
                actuals.append(2)  # 主胜
            elif result == -1:
                actuals.append(0)  # 主负
            else:
                actuals.append(1)  # 平局
        
        accuracy = accuracy_score(actuals, predictions)
        
        if verbose:
            print(f"增强模型准确率: {accuracy:.3f}")
            print("\n增强模型分类报告:")
            print(classification_report(actuals, predictions, target_names=['客胜', '平局', '主胜']))
        
        return accuracy
    
    def predict_enhanced_match(self, home_team, away_team, features):
        """预测增强比赛"""
        if self.trace is None:
            raise ValueError("模型尚未训练")
        
        home_idx = self.team_to_idx[home_team]
        away_idx = self.team_to_idx[away_team]
        
        # 从后验分布采样
        alpha_base_samples = self.trace.posterior['log_alpha_base'].values.reshape(-1, len(self.teams))
        alpha_base_samples = np.exp(alpha_base_samples)
        
        feature_weights_samples = self.trace.posterior['feature_weights'].values.reshape(-1, len(self.feature_names))
        gamma_samples = self.trace.posterior['gamma'].values.flatten()
        home_adv_samples = self.trace.posterior['home_advantage_base'].values.flatten()
        
        # 计算特征效应
        feature_vector = np.array([features.get(fname, 0) for fname in self.feature_names])
        feature_effects = np.dot(feature_weights_samples, feature_vector)
        
        # 计算调整后的强度
        alpha_home = alpha_base_samples[:, home_idx] * np.exp(feature_effects) * np.exp(home_adv_samples)
        alpha_away = alpha_base_samples[:, away_idx] * np.exp(-feature_effects)
        
        total = alpha_home + alpha_away + gamma_samples
        
        p_home_win = np.mean(alpha_home / total)
        p_draw = np.mean(gamma_samples / total)
        p_away_win = np.mean(alpha_away / total)
        
        # 归一化
        total_prob = p_home_win + p_draw + p_away_win
        p_home_win /= total_prob
        p_draw /= total_prob
        p_away_win /= total_prob
        
        return {
            'p_home_win': p_home_win,
            'p_draw': p_draw,
            'p_away_win': p_away_win,
            'predicted_result': np.argmax([p_away_win, p_draw, p_home_win])
        }
    
    def run_enhanced_analysis(self, optimize_hyperparams=True):
        """运行增强分析"""
        print("🚀 增强版贝叶斯BTD模型分析")
        print("="*50)
        
        # 1. 加载数据
        matches = self.load_and_prepare_data()
        
        # 2. 提取增强特征
        enhanced_matches = self.extract_enhanced_features(matches)
        
        # 3. 超参数优化（可选）
        if optimize_hyperparams:
            param_ranges = {
                'alpha_prior_sigma': (0.2, 0.8),
                'gamma_alpha': (1.0, 4.0),
                'gamma_beta': (1.0, 4.0),
                'feature_weight_sigma': (0.1, 0.5),
                'form_decay': (0.6, 0.9)
            }
            self.hyperparameter_optimization(enhanced_matches, param_ranges)
        
        # 4. 拟合最终模型
        trace = self.fit_enhanced_model(enhanced_matches)
        
        # 5. 评估性能
        accuracy = self.evaluate_enhanced_model(enhanced_matches)
        
        # 6. 分析特征重要性
        self.analyze_feature_importance()
        
        print(f"\n✅ 增强分析完成!")
        print(f"🎯 最终准确率: {accuracy:.1%}")
        
        return accuracy
    
    def analyze_feature_importance(self):
        """分析特征重要性"""
        if self.trace is None:
            return
        
        print("\n📊 特征重要性分析:")
        
        feature_weights = self.trace.posterior['feature_weights'].values.reshape(-1, len(self.feature_names))
        
        print("特征名称                权重均值    权重标准差   重要性")
        print("-" * 55)
        
        for i, fname in enumerate(self.feature_names):
            weight_mean = np.mean(feature_weights[:, i])
            weight_std = np.std(feature_weights[:, i])
            importance = abs(weight_mean) / (weight_std + 1e-6)
            
            print(f"{fname:20s}  {weight_mean:8.3f}  {weight_std:10.3f}  {importance:8.2f}")

def main():
    """主函数"""
    # 创建增强模型
    model = EnhancedBTDModel()
    
    # 运行分析
    accuracy = model.run_enhanced_analysis(optimize_hyperparams=True)
    
    print(f"\n💡 增强BTD模型特点:")
    print(f"✅ 融入10个增强特征")
    print(f"✅ 自动超参数优化")
    print(f"✅ 特征重要性分析")
    print(f"✅ 更强的预测能力")

if __name__ == "__main__":
    main()
