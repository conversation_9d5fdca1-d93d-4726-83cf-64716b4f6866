#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带概率阈值优化的BTD模型
专门改进平局预测能力
"""

import pandas as pd
import numpy as np
import pymc as pm
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

class ThresholdOptimizedBTD:
    """带阈值优化的BTD模型"""
    
    def __init__(self):
        self.teams = []
        self.team_to_idx = {}
        self.trace = None
        self.best_thresholds = None
        self.best_accuracy = 0
        
    def load_data(self):
        """加载数据"""
        print("📊 加载比赛数据...")
        
        try:
            df_results = pd.read_csv('maches2.csv', encoding='utf-8')
            print("✅ 成功读取 maches2.csv")
        except:
            try:
                df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
                print("✅ 成功读取 合并后的比赛数据.csv")
            except Exception as e:
                print(f"❌ 文件读取失败: {e}")
                return []
        
        matches = []
        teams = set()
        
        print("数据列名:", df_results.columns.tolist())
        
        for _, row in df_results.iterrows():
            # 尝试不同的列名组合
            home_team_cols = ['参赛队伍1', 'home_team', '主队']
            away_team_cols = ['参赛队伍2', 'away_team', '客队']
            home_goals_cols = ['参赛队伍1进球数', '参赛队伍1进球 数', 'home_goals']
            away_goals_cols = ['参赛队伍2进球数', 'away_goals']
            
            home_team = None
            away_team = None
            home_goals = None
            away_goals = None
            
            # 查找各字段
            for col in home_team_cols:
                if col in row and pd.notna(row[col]):
                    home_team = row[col]
                    break
            
            for col in away_team_cols:
                if col in row and pd.notna(row[col]):
                    away_team = row[col]
                    break
            
            for col in home_goals_cols:
                if col in row and pd.notna(row[col]):
                    home_goals = int(row[col])
                    break
            
            for col in away_goals_cols:
                if col in row and pd.notna(row[col]):
                    away_goals = int(row[col])
                    break
            
            if all(x is not None for x in [home_team, away_team, home_goals, away_goals]):
                teams.add(home_team)
                teams.add(away_team)
                
                if home_goals > away_goals:
                    result = 1  # 主胜
                elif home_goals < away_goals:
                    result = -1  # 主负
                else:
                    result = 0  # 平局
                
                matches.append({
                    'home_team': home_team,
                    'away_team': away_team,
                    'home_goals': home_goals,
                    'away_goals': away_goals,
                    'result': result
                })
        
        self.teams = sorted(list(teams))
        self.team_to_idx = {team: i for i, team in enumerate(self.teams)}
        
        print(f"队伍数量: {len(self.teams)}")
        print(f"比赛数量: {len(matches)}")
        
        # 统计结果分布
        results = [m['result'] for m in matches]
        home_wins = sum(1 for r in results if r == 1)
        draws = sum(1 for r in results if r == 0)
        away_wins = sum(1 for r in results if r == -1)
        
        print(f"结果分布: 主胜{home_wins}场({home_wins/len(matches):.1%}), "
              f"平局{draws}场({draws/len(matches):.1%}), "
              f"客胜{away_wins}场({away_wins/len(matches):.1%})")
        
        return matches
    
    def calculate_prior_strengths(self, matches):
        """计算先验强度"""
        team_stats = {team: {'wins': 0, 'draws': 0, 'losses': 0, 'games': 0} for team in self.teams}
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            result = match['result']
            
            team_stats[home_team]['games'] += 1
            team_stats[away_team]['games'] += 1
            
            if result == 1:  # 主胜
                team_stats[home_team]['wins'] += 1
                team_stats[away_team]['losses'] += 1
            elif result == -1:  # 主负
                team_stats[away_team]['wins'] += 1
                team_stats[home_team]['losses'] += 1
            else:  # 平局
                team_stats[home_team]['draws'] += 1
                team_stats[away_team]['draws'] += 1
        
        prior_strengths = []
        for team in self.teams:
            stats = team_stats[team]
            if stats['games'] > 0:
                win_rate = (stats['wins'] + 0.5 * stats['draws']) / stats['games']
                strength = max(win_rate * 2 + 0.1, 0.1)
            else:
                strength = 1.0
            prior_strengths.append(strength)
        
        return np.array(prior_strengths)
    
    def fit_btd_model(self, matches):
        """拟合基础BTD模型"""
        print("🔥 拟合BTD模型...")
        
        prior_strengths = self.calculate_prior_strengths(matches)
        n_teams = len(self.teams)
        
        # 准备数据
        home_teams = np.array([self.team_to_idx[m['home_team']] for m in matches])
        away_teams = np.array([self.team_to_idx[m['away_team']] for m in matches])
        results = np.array([m['result'] for m in matches])
        
        with pm.Model() as model:
            # 队伍强度参数
            log_alpha = pm.Normal('log_alpha', 
                                mu=np.log(prior_strengths), 
                                sigma=0.5,
                                shape=n_teams)
            alpha = pm.Deterministic('alpha', pm.math.exp(log_alpha))
            
            # 平局参数
            gamma = pm.Gamma('gamma', alpha=2.0, beta=2.0)
            
            # 主场优势
            home_advantage = pm.Normal('home_advantage', mu=0.1, sigma=0.1)
            
            # BTD概率计算
            alpha_home = alpha[home_teams] * pm.math.exp(home_advantage)
            alpha_away = alpha[away_teams]
            total = alpha_home + alpha_away + gamma
            
            p_home_win = alpha_home / total
            p_draw = gamma / total
            p_away_win = alpha_away / total
            
            # 似然函数
            categorical_results = np.where(results == -1, 0, np.where(results == 0, 1, 2))
            probs_matrix = pm.math.stack([p_away_win, p_draw, p_home_win], axis=1)
            
            likelihood = pm.Categorical('likelihood', p=probs_matrix, observed=categorical_results)
        
        # 采样
        with model:
            self.trace = pm.sample(
                draws=1200,
                tune=800,
                chains=2,
                cores=1,
                random_seed=42,
                return_inferencedata=True,
                progressbar=True
            )
        
        print("✅ BTD模型拟合完成")
        return self.trace
    
    def predict_probabilities(self, matches):
        """预测所有比赛的概率"""
        if self.trace is None:
            raise ValueError("模型尚未训练")
        
        probabilities = []
        
        for match in matches:
            home_idx = self.team_to_idx[match['home_team']]
            away_idx = self.team_to_idx[match['away_team']]
            
            # 从后验分布中采样
            alpha_samples = self.trace.posterior['alpha'].values.reshape(-1, len(self.teams))
            gamma_samples = self.trace.posterior['gamma'].values.flatten()
            home_adv_samples = self.trace.posterior['home_advantage'].values.flatten()
            
            # 计算预测概率
            alpha_home = alpha_samples[:, home_idx] * np.exp(home_adv_samples)
            alpha_away = alpha_samples[:, away_idx]
            total = alpha_home + alpha_away + gamma_samples
            
            p_home_win = np.mean(alpha_home / total)
            p_draw = np.mean(gamma_samples / total)
            p_away_win = np.mean(alpha_away / total)
            
            # 归一化
            total_prob = p_home_win + p_draw + p_away_win
            p_home_win /= total_prob
            p_draw /= total_prob
            p_away_win /= total_prob
            
            probabilities.append({
                'p_home_win': p_home_win,
                'p_draw': p_draw,
                'p_away_win': p_away_win
            })
        
        return probabilities
    
    def apply_threshold_strategy(self, probabilities, thresholds):
        """应用阈值策略进行预测"""
        predictions = []
        
        for prob in probabilities:
            p_home = prob['p_home_win']
            p_draw = prob['p_draw']
            p_away = prob['p_away_win']
            
            # 策略1: 双阈值方法
            if len(thresholds) == 2:
                win_threshold, draw_threshold = thresholds
                
                # 如果胜负概率都不够高，且平局概率超过阈值，预测平局
                if max(p_home, p_away) < win_threshold and p_draw > draw_threshold:
                    predictions.append(1)  # 平局
                else:
                    # 否则选择概率最大的结果
                    predictions.append(np.argmax([p_away, p_draw, p_home]))
            
            # 策略2: 单阈值方法
            else:
                threshold = thresholds[0]
                
                # 如果最大概率小于阈值，预测平局
                if max(p_home, p_away, p_draw) < threshold:
                    predictions.append(1)  # 平局
                else:
                    predictions.append(np.argmax([p_away, p_draw, p_home]))
        
        return predictions
    
    def evaluate_threshold_strategy(self, matches, thresholds):
        """评估阈值策略"""
        try:
            # 获取概率预测
            probabilities = self.predict_probabilities(matches)
            
            # 应用阈值策略
            predictions = self.apply_threshold_strategy(probabilities, thresholds)
            
            # 准备真实标签
            actuals = []
            for match in matches:
                result = match['result']
                if result == 1:
                    actuals.append(2)  # 主胜
                elif result == -1:
                    actuals.append(0)  # 客胜
                else:
                    actuals.append(1)  # 平局
            
            # 计算准确率
            accuracy = accuracy_score(actuals, predictions)
            
            return accuracy
            
        except Exception as e:
            print(f"评估失败: {e}")
            return 0.0
    
    def optimize_thresholds(self, matches):
        """优化阈值参数"""
        print("🎛️ 优化概率阈值...")
        
        def objective(thresholds):
            return -self.evaluate_threshold_strategy(matches, thresholds)
        
        best_accuracy = 0
        best_thresholds = None
        
        # 尝试不同的阈值策略
        strategies = [
            {
                'name': '双阈值策略',
                'bounds': [(0.35, 0.55), (0.15, 0.35)],  # [win_threshold, draw_threshold]
                'initial': [0.45, 0.25]
            },
            {
                'name': '单阈值策略', 
                'bounds': [(0.30, 0.50)],  # [general_threshold]
                'initial': [0.40]
            }
        ]
        
        for strategy in strategies:
            print(f"\n测试 {strategy['name']}:")
            
            try:
                result = minimize(
                    objective,
                    strategy['initial'],
                    bounds=strategy['bounds'],
                    method='L-BFGS-B'
                )
                
                if result.success:
                    accuracy = -result.fun
                    print(f"  优化成功，准确率: {accuracy:.3f}")
                    print(f"  最优阈值: {result.x}")
                    
                    if accuracy > best_accuracy:
                        best_accuracy = accuracy
                        best_thresholds = result.x
                        print(f"  🎯 新的最佳策略!")
                else:
                    print(f"  优化失败: {result.message}")
                    
            except Exception as e:
                print(f"  策略测试失败: {e}")
        
        if best_thresholds is not None:
            self.best_thresholds = best_thresholds
            self.best_accuracy = best_accuracy
            print(f"\n✅ 阈值优化完成!")
            print(f"最佳准确率: {best_accuracy:.3f}")
            print(f"最佳阈值: {best_thresholds}")
            return True
        else:
            print(f"\n❌ 阈值优化失败")
            return False
    
    def final_evaluation(self, matches):
        """最终评估"""
        print("\n📊 最终模型评估:")
        
        # 基础BTD预测
        probabilities = self.predict_probabilities(matches)
        basic_predictions = [np.argmax([p['p_away_win'], p['p_draw'], p['p_home_win']]) 
                           for p in probabilities]
        
        # 阈值优化预测
        if self.best_thresholds is not None:
            threshold_predictions = self.apply_threshold_strategy(probabilities, self.best_thresholds)
        else:
            threshold_predictions = basic_predictions
        
        # 准备真实标签
        actuals = []
        for match in matches:
            result = match['result']
            if result == 1:
                actuals.append(2)  # 主胜
            elif result == -1:
                actuals.append(0)  # 客胜
            else:
                actuals.append(1)  # 平局
        
        # 评估基础模型
        basic_accuracy = accuracy_score(actuals, basic_predictions)
        print(f"\n基础BTD模型:")
        print(f"准确率: {basic_accuracy:.3f}")
        print("分类报告:")
        print(classification_report(actuals, basic_predictions, 
                                  target_names=['客胜', '平局', '主胜']))
        
        # 评估阈值优化模型
        threshold_accuracy = accuracy_score(actuals, threshold_predictions)
        print(f"\n阈值优化模型:")
        print(f"准确率: {threshold_accuracy:.3f}")
        print("分类报告:")
        print(classification_report(actuals, threshold_predictions, 
                                  target_names=['客胜', '平局', '主胜']))
        
        # 改进分析
        improvement = threshold_accuracy - basic_accuracy
        print(f"\n📈 性能改进:")
        print(f"准确率提升: {improvement:.3f} ({improvement/basic_accuracy:.1%})")
        
        # 混淆矩阵对比
        print(f"\n混淆矩阵对比:")
        print("基础模型:")
        print(confusion_matrix(actuals, basic_predictions))
        print("阈值优化模型:")
        print(confusion_matrix(actuals, threshold_predictions))
        
        return threshold_accuracy
    
    def run_basic_btd_analysis(self):
        """运行基础BTD分析"""
        print("🎯 基础BTD模型分析")
        print("="*50)

        # 1. 加载数据
        matches = self.load_data()
        if not matches:
            return

        # 2. 拟合BTD模型
        self.fit_btd_model(matches)

        # 3. 基础评估
        print("\n📊 基础BTD模型评估:")

        # 获取概率预测
        probabilities = self.predict_probabilities(matches)
        basic_predictions = [np.argmax([p['p_away_win'], p['p_draw'], p['p_home_win']])
                           for p in probabilities]

        # 准备真实标签
        actuals = []
        for match in matches:
            result = match['result']
            if result == 1:
                actuals.append(2)  # 主胜
            elif result == -1:
                actuals.append(0)  # 客胜
            else:
                actuals.append(1)  # 平局

        # 评估基础模型
        basic_accuracy = accuracy_score(actuals, basic_predictions)
        print(f"准确率: {basic_accuracy:.3f}")
        print("分类报告:")
        print(classification_report(actuals, basic_predictions,
                                  target_names=['客胜', '平局', '主胜']))

        print("混淆矩阵:")
        print(confusion_matrix(actuals, basic_predictions))

        # 4. 平局分析
        self.analyze_draw_patterns(matches, probabilities, actuals)

        print(f"\n💡 总结:")
        print(f"📊 基础BTD模型准确率: {basic_accuracy:.1%}")
        print(f"🎯 平局预测仍然是挑战，需要新的方法")

        return basic_accuracy

    def analyze_draw_patterns(self, matches, probabilities, actuals):
        """分析平局模式"""
        print(f"\n🔍 平局模式分析:")

        # 找出实际的平局比赛
        draw_indices = [i for i, actual in enumerate(actuals) if actual == 1]

        print(f"实际平局比赛数: {len(draw_indices)}")

        if len(draw_indices) > 0:
            print(f"\n平局比赛的概率分布:")
            for i in draw_indices:
                match = matches[i]
                prob = probabilities[i]
                print(f"{match['home_team']} vs {match['away_team']}: "
                      f"主胜{prob['p_home_win']:.3f}, 平局{prob['p_draw']:.3f}, 客胜{prob['p_away_win']:.3f}")

            # 分析平局比赛的特征
            draw_probs = [probabilities[i] for i in draw_indices]
            avg_home_prob = np.mean([p['p_home_win'] for p in draw_probs])
            avg_draw_prob = np.mean([p['p_draw'] for p in draw_probs])
            avg_away_prob = np.mean([p['p_away_win'] for p in draw_probs])

            print(f"\n平局比赛的平均概率:")
            print(f"主胜: {avg_home_prob:.3f}, 平局: {avg_draw_prob:.3f}, 客胜: {avg_away_prob:.3f}")

            # 分析实力差距
            print(f"\n平局比赛的实力对比:")
            for i in draw_indices:
                match = matches[i]
                print(f"{match['home_team']} {match['home_goals']}-{match['away_goals']} {match['away_team']}")

        print(f"\n💡 观察:")
        print(f"- 平局比赛的模式分析有助于理解预测困难的原因")
        print(f"- 可以基于这些模式设计更好的预测方法")

def main():
    """主函数"""
    model = ThresholdOptimizedBTD()
    final_accuracy = model.run_basic_btd_analysis()

    print(f"\n🎉 分析完成!")
    print(f"基础BTD模型为我们提供了很好的基准")
    print(f"现在可以基于平局模式分析设计更好的方法")

if __name__ == "__main__":
    main()
