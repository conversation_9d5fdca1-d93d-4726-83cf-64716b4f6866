#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
贝叶斯泊松回归与分层建模
考虑攻防策略交互，引入球队进攻/防守能力的全局分布
"""

import pandas as pd
import numpy as np
import pymc as pm
import arviz as az
from scipy import stats
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

class BayesianPoissonModel:
    """贝叶斯分层泊松回归模型"""
    
    def __init__(self):
        self.model = None
        self.trace = None
        self.teams = []
        self.team_to_idx = {}
        self.idx_to_team = {}
        
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("📊 加载比赛数据...")
        
        df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
        
        matches = []
        teams = set()
        
        for _, row in df_results.iterrows():
            if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
                goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
                goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
                
                if pd.notna(row[goals1_col]) and pd.notna(row[goals2_col]):
                    home_team = row['参赛队伍1']
                    away_team = row['参赛队伍2']
                    home_goals = int(row[goals1_col])
                    away_goals = int(row[goals2_col])
                    
                    teams.add(home_team)
                    teams.add(away_team)
                    
                    matches.append({
                        'home_team': home_team,
                        'away_team': away_team,
                        'home_goals': home_goals,
                        'away_goals': away_goals
                    })
        
        # 建立队伍索引
        self.teams = sorted(list(teams))
        self.team_to_idx = {team: i for i, team in enumerate(self.teams)}
        self.idx_to_team = {i: team for i, team in enumerate(self.teams)}
        
        print(f"队伍数量: {len(self.teams)}")
        print(f"比赛数量: {len(matches)}")
        
        # 计算基础统计
        total_goals = sum(m['home_goals'] + m['away_goals'] for m in matches)
        avg_goals_per_match = total_goals / len(matches)
        print(f"场均总进球: {avg_goals_per_match:.2f}")
        
        return matches
    
    def calculate_team_priors(self, matches):
        """计算队伍攻防能力的先验分布"""
        print("🧮 计算队伍攻防先验...")
        
        # 统计每队的攻防数据
        team_stats = {team: {'goals_for': 0, 'goals_against': 0, 'games': 0, 'home_games': 0, 'away_games': 0} 
                     for team in self.teams}
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            home_goals = match['home_goals']
            away_goals = match['away_goals']
            
            # 主队统计
            team_stats[home_team]['goals_for'] += home_goals
            team_stats[home_team]['goals_against'] += away_goals
            team_stats[home_team]['games'] += 1
            team_stats[home_team]['home_games'] += 1
            
            # 客队统计
            team_stats[away_team]['goals_for'] += away_goals
            team_stats[away_team]['goals_against'] += home_goals
            team_stats[away_team]['games'] += 1
            team_stats[away_team]['away_games'] += 1
        
        # 计算攻防能力
        attack_rates = []
        defense_rates = []
        
        for team in self.teams:
            stats = team_stats[team]
            if stats['games'] > 0:
                attack_rate = stats['goals_for'] / stats['games']
                defense_rate = stats['goals_against'] / stats['games']
            else:
                attack_rate = 1.0  # 默认值
                defense_rate = 1.0
            
            attack_rates.append(attack_rate)
            defense_rates.append(defense_rate)
        
        attack_rates = np.array(attack_rates)
        defense_rates = np.array(defense_rates)
        
        print(f"攻击力范围: [{attack_rates.min():.2f}, {attack_rates.max():.2f}]")
        print(f"防守力范围: [{defense_rates.min():.2f}, {defense_rates.max():.2f}]")
        
        return attack_rates, defense_rates, team_stats
    
    def build_hierarchical_model(self, matches, attack_priors, defense_priors):
        """构建分层贝叶斯泊松模型"""
        print("🏗️ 构建分层贝叶斯泊松模型...")
        
        n_teams = len(self.teams)
        n_matches = len(matches)
        
        # 准备数据
        home_teams = np.array([self.team_to_idx[m['home_team']] for m in matches])
        away_teams = np.array([self.team_to_idx[m['away_team']] for m in matches])
        home_goals = np.array([m['home_goals'] for m in matches])
        away_goals = np.array([m['away_goals'] for m in matches])
        
        with pm.Model() as model:
            # 全局超参数 (分层建模的核心)
            # 攻击力的全局分布
            mu_attack = pm.Normal('mu_attack', mu=np.log(np.mean(attack_priors)), sigma=0.5)
            sigma_attack = pm.HalfNormal('sigma_attack', sigma=0.3)
            
            # 防守力的全局分布  
            mu_defense = pm.Normal('mu_defense', mu=np.log(np.mean(defense_priors)), sigma=0.5)
            sigma_defense = pm.HalfNormal('sigma_defense', sigma=0.3)
            
            # 队伍特定参数 (从全局分布中采样)
            # 攻击力参数 (对数正态分布)
            log_attack = pm.Normal('log_attack', 
                                 mu=mu_attack, 
                                 sigma=sigma_attack, 
                                 shape=n_teams)
            attack = pm.Deterministic('attack', pm.math.exp(log_attack))
            
            # 防守力参数 (对数正态分布，值越小防守越好)
            log_defense = pm.Normal('log_defense', 
                                  mu=mu_defense, 
                                  sigma=sigma_defense, 
                                  shape=n_teams)
            defense = pm.Deterministic('defense', pm.math.exp(log_defense))
            
            # 主场优势
            home_advantage = pm.Normal('home_advantage', mu=0.2, sigma=0.1)
            
            # 攻防交互参数 (关键创新)
            interaction_strength = pm.HalfNormal('interaction_strength', sigma=0.2)
            
            # 计算期望进球数 (考虑攻防交互)
            # λ_{A|B} = attack_A * defense_B * home_advantage * interaction
            lambda_home = (attack[home_teams] * 
                          defense[away_teams] * 
                          pm.math.exp(home_advantage) *
                          pm.math.exp(-interaction_strength * pm.math.abs(attack[home_teams] - attack[away_teams])))
            
            lambda_away = (attack[away_teams] * 
                          defense[home_teams] *
                          pm.math.exp(-interaction_strength * pm.math.abs(attack[away_teams] - attack[home_teams])))
            
            # 泊松似然
            home_goals_obs = pm.Poisson('home_goals_obs', mu=lambda_home, observed=home_goals)
            away_goals_obs = pm.Poisson('away_goals_obs', mu=lambda_away, observed=away_goals)
            
            # 添加过度离散参数 (处理泊松分布的局限性)
            overdispersion = pm.HalfNormal('overdispersion', sigma=0.1)
            
            # 负二项分布作为替代 (更灵活)
            alpha_home = lambda_home / overdispersion
            alpha_away = lambda_away / overdispersion
            
            # 可选：使用负二项分布
            # home_goals_nb = pm.NegativeBinomial('home_goals_nb', mu=lambda_home, alpha=alpha_home, observed=home_goals)
            # away_goals_nb = pm.NegativeBinomial('away_goals_nb', mu=lambda_away, alpha=alpha_away, observed=away_goals)
        
        self.model = model
        return model
    
    def fit_model(self, matches, attack_priors, defense_priors, n_samples=1500, n_tune=1000):
        """拟合模型"""
        print("🔥 使用MCMC拟合分层模型...")
        
        model = self.build_hierarchical_model(matches, attack_priors, defense_priors)
        
        with model:
            # MCMC采样
            self.trace = pm.sample(
                draws=n_samples,
                tune=n_tune,
                chains=2,
                cores=1,
                random_seed=42,
                return_inferencedata=True,
                progressbar=True
            )
        
        print("✅ MCMC采样完成")
        return self.trace
    
    def predict_match_goals(self, home_team, away_team):
        """预测比赛进球数"""
        if self.trace is None:
            raise ValueError("模型尚未训练")
        
        home_idx = self.team_to_idx[home_team]
        away_idx = self.team_to_idx[away_team]
        
        # 从后验分布中采样
        attack_samples = self.trace.posterior['attack'].values.reshape(-1, len(self.teams))
        defense_samples = self.trace.posterior['defense'].values.reshape(-1, len(self.teams))
        home_adv_samples = self.trace.posterior['home_advantage'].values.flatten()
        interaction_samples = self.trace.posterior['interaction_strength'].values.flatten()
        
        # 计算期望进球数
        lambda_home = (attack_samples[:, home_idx] * 
                      defense_samples[:, away_idx] * 
                      np.exp(home_adv_samples) *
                      np.exp(-interaction_samples * np.abs(attack_samples[:, home_idx] - attack_samples[:, away_idx])))
        
        lambda_away = (attack_samples[:, away_idx] * 
                      defense_samples[:, home_idx] *
                      np.exp(-interaction_samples * np.abs(attack_samples[:, away_idx] - attack_samples[:, home_idx])))
        
        # 预测期望进球数
        expected_home_goals = np.mean(lambda_home)
        expected_away_goals = np.mean(lambda_away)
        
        # 计算胜负平概率 (通过模拟)
        n_simulations = 1000
        home_wins = 0
        draws = 0
        away_wins = 0
        
        for i in range(n_simulations):
            # 随机选择一组参数
            idx = np.random.randint(len(lambda_home))
            
            # 模拟比赛结果
            sim_home_goals = np.random.poisson(lambda_home[idx])
            sim_away_goals = np.random.poisson(lambda_away[idx])
            
            if sim_home_goals > sim_away_goals:
                home_wins += 1
            elif sim_home_goals < sim_away_goals:
                away_wins += 1
            else:
                draws += 1
        
        p_home_win = home_wins / n_simulations
        p_draw = draws / n_simulations
        p_away_win = away_wins / n_simulations
        
        return {
            'expected_home_goals': expected_home_goals,
            'expected_away_goals': expected_away_goals,
            'p_home_win': p_home_win,
            'p_draw': p_draw,
            'p_away_win': p_away_win,
            'predicted_result': np.argmax([p_away_win, p_draw, p_home_win])
        }
    
    def evaluate_model(self, matches):
        """评估模型性能"""
        print("📊 评估模型性能...")
        
        predictions = []
        actuals = []
        goal_predictions = []
        actual_goals = []
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            actual_home_goals = match['home_goals']
            actual_away_goals = match['away_goals']
            
            # 预测
            pred = self.predict_match_goals(home_team, away_team)
            
            # 结果预测
            predictions.append(pred['predicted_result'])
            
            # 实际结果
            if actual_home_goals > actual_away_goals:
                actuals.append(2)  # 主胜
            elif actual_home_goals < actual_away_goals:
                actuals.append(0)  # 主负
            else:
                actuals.append(1)  # 平局
            
            # 进球数预测
            goal_predictions.append([pred['expected_home_goals'], pred['expected_away_goals']])
            actual_goals.append([actual_home_goals, actual_away_goals])
        
        # 计算准确率
        accuracy = accuracy_score(actuals, predictions)
        
        # 计算进球数预测误差
        goal_predictions = np.array(goal_predictions)
        actual_goals = np.array(actual_goals)
        
        mae_home = np.mean(np.abs(goal_predictions[:, 0] - actual_goals[:, 0]))
        mae_away = np.mean(np.abs(goal_predictions[:, 1] - actual_goals[:, 1]))
        
        print(f"结果预测准确率: {accuracy:.3f}")
        print(f"主队进球MAE: {mae_home:.3f}")
        print(f"客队进球MAE: {mae_away:.3f}")
        
        print("\n分类报告:")
        print(classification_report(actuals, predictions, target_names=['客胜', '平局', '主胜']))
        
        return accuracy, mae_home, mae_away
    
    def get_team_abilities(self):
        """获取队伍攻防能力"""
        if self.trace is None:
            raise ValueError("模型尚未训练")
        
        attack_samples = self.trace.posterior['attack'].values.reshape(-1, len(self.teams))
        defense_samples = self.trace.posterior['defense'].values.reshape(-1, len(self.teams))
        
        team_abilities = []
        for i, team in enumerate(self.teams):
            attack_mean = np.mean(attack_samples[:, i])
            attack_std = np.std(attack_samples[:, i])
            defense_mean = np.mean(defense_samples[:, i])
            defense_std = np.std(defense_samples[:, i])
            
            team_abilities.append({
                'team': team,
                'attack_mean': attack_mean,
                'attack_std': attack_std,
                'defense_mean': defense_mean,
                'defense_std': defense_std,
                'overall_strength': attack_mean / defense_mean  # 攻防比
            })
        
        # 按综合实力排序
        team_abilities.sort(key=lambda x: x['overall_strength'], reverse=True)
        
        print("\n🏆 队伍攻防能力排名:")
        print("排名  队伍      攻击力    防守力    攻防比")
        print("-" * 45)
        for i, ta in enumerate(team_abilities, 1):
            print(f"{i:2d}   {ta['team']:8s}  {ta['attack_mean']:.3f}    {ta['defense_mean']:.3f}    {ta['overall_strength']:.3f}")
        
        return team_abilities
    
    def run_poisson_analysis(self):
        """运行完整的泊松分析"""
        print("🎯 贝叶斯分层泊松回归分析")
        print("="*50)
        
        # 1. 加载数据
        matches = self.load_and_prepare_data()
        
        # 2. 计算先验
        attack_priors, defense_priors, team_stats = self.calculate_team_priors(matches)
        
        # 3. 拟合模型
        trace = self.fit_model(matches, attack_priors, defense_priors)
        
        # 4. 评估性能
        accuracy, mae_home, mae_away = self.evaluate_model(matches)
        
        # 5. 获取队伍能力
        team_abilities = self.get_team_abilities()
        
        # 6. 显示关键参数
        home_adv_mean = np.mean(self.trace.posterior['home_advantage'].values)
        interaction_mean = np.mean(self.trace.posterior['interaction_strength'].values)
        
        print(f"\n📈 模型参数:")
        print(f"主场优势: {home_adv_mean:.3f}")
        print(f"攻防交互强度: {interaction_mean:.3f}")
        
        print(f"\n✅ 泊松模型分析完成!")
        print(f"🎯 结果准确率: {accuracy:.1%}")
        print(f"📊 进球预测MAE: {(mae_home + mae_away)/2:.3f}")
        
        return accuracy, mae_home, mae_away, team_abilities

def main():
    """主函数"""
    try:
        model = BayesianPoissonModel()
        accuracy, mae_home, mae_away, team_abilities = model.run_poisson_analysis()
        
        print(f"\n💡 分层泊松模型特点:")
        print(f"✅ 考虑攻防策略交互")
        print(f"✅ 分层建模处理队伍间差异")
        print(f"✅ 贝叶斯框架量化不确定性")
        print(f"✅ 同时预测进球数和比赛结果")
        
    except Exception as e:
        print(f"❌ 模型运行出错: {e}")
        print("请确保已安装 pymc 库: pip install pymc")

if __name__ == "__main__":
    main()
