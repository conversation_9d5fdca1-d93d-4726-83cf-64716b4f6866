{"learner": {"attributes": {"best_iteration": "3", "best_score": "1.086574998768893"}, "feature_names": [], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "42"}, "iteration_indptr": [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42], "tree_info": [0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, 1, 2], "trees": [{"base_weights": [-0.26896557, -0.38461542, -0.0, -0.0, -0.047191016], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.7575598, 0.27917027, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.7587697, -0.8916615, -0.0, -0.0, -0.047191016], "split_indices": [9, 9, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.111111, 11.999999, 3.111111, 3.111111, 8.888888], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.22147655, -0.048387103, 0.046153806, -0.008108111, 0.04054054], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 1, "left_children": [1, -1, 3, -1, -1], "loss_changes": [1.622659, 0.0, 0.687318, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [0.6405209, -0.048387103, -0.35983792, -0.008108111, 0.04054054], "split_indices": [17, 0, 3, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.555554, 9.333333, 6.222222, 3.111111, 3.111111], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.31578943, -0.1643836, 0.09130435, -0.040000003, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, -1, -1, -1], "loss_changes": [5.1367984, 0.5808219, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.0005570686, 0.21070339, 0.09130435, -0.040000003, -0.0], "split_indices": [2, 21, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [13.777777, 7.1111107, 6.666666, 3.9999998, 3.111111], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.040825088, -0.27726677, 0.025099462, -0.0, -0.041462626], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.3483672, 0.49780786, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.513182, -0.70710677, 0.025099462, -0.0, -0.041462626], "split_indices": [11, 12, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [17.152649, 11.395938, 5.7567105, 3.9570627, 7.438875], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.2632777, -0.04949864, -0.0, 0.038606383, -0.015931983], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 4, "left_children": [1, -1, 3, -1, -1], "loss_changes": [1.5395374, 0.0, 0.7355102, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [0.6405209, -0.04949864, 0.19425717, 0.038606383, -0.015931983], "split_indices": [17, 0, 32, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [17.175035, 10.42535, 6.749685, 3.1517742, 3.5979104], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.29637432, -0.0, 0.079393074, 0.02055153, -0.16729493, -0.036242362, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [2.9901972, 0.4789378, 0.0, 0.0, 0.36887664, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [0.04118206, -0.3502842, 0.079393074, 0.02055153, 0.056793895, -0.036242362, -0.0], "split_indices": [2, 22, 0, 0, 39, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [18.548603, 12.080559, 6.468045, 4.955308, 7.125251, 3.5396192, 3.5856314], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.16947596, 0.059314817, -0.0, -0.038610544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, -1, -1, -1], "loss_changes": [2.1058433, 0.39354303, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.7960122, -0.059169956, 0.059314817, -0.0, -0.038610544], "split_indices": [1, 20, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [14.893979, 10.878198, 4.015782, 6.949819, 3.9283783], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.15110034, -0.0, -0.045447577, 0.22228763, -0.032548953, -0.0, 0.04997937], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [0.9630955, 0.83182657, 0.0, 0.7473652, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [0.25541353, 0.97971106, -0.045447577, -0.3502842, -0.032548953, -0.0, 0.04997937], "split_indices": [7, 11, 0, 22, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [15.628138, 10.127294, 5.5008435, 7.0128675, 3.1144261, 3.4359107, 3.5769567], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.25836843, -0.0, 0.06649797, 0.016994188, -0.024930218], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.9269042, 0.48603374, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.22644159, -0.54391986, 0.06649797, 0.016994188, -0.024930218], "split_indices": [6, 19, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [14.189938, 8.539303, 5.650635, 4.079463, 4.45984], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.040267862, -0.297773, 0.023036731, -0.04935142, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.3024592, 0.8384637, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.513182, 1.7033868, 0.023036731, -0.04935142, -0.0], "split_indices": [11, 25, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.7378, 9.906581, 5.8312197, 6.4132223, 3.4933581], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.1911268, -0.0, -0.04694904, 0.013806261, -0.109391466, -0.031111855, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [0.9247778, 0.1925962, 0.0, 0.0, 0.30268672, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [0.42700037, 0.19425717, -0.04694904, 0.013806261, -0.024334414, -0.031111855, -0.0], "split_indices": [0, 32, 0, 0, 4, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [17.102997, 10.907364, 6.1956334, 4.375819, 6.531545, 3.0582044, 3.4733407], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.263655, 0.5813016, -0.0049326327, -0.0, 0.078217305, 0.012557536, -0.026964704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [2.108895, 0.41542506, 0.4888815, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [0.054014612, -0.13278438, 0.17008886, -0.0, 0.078217305, 0.012557536, -0.026964704], "split_indices": [33, 36, 40, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [16.929335, 8.928676, 8.000658, 3.765758, 5.162918, 3.1805592, 4.8200984], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.16692261, -0.23477685, -0.0, 0.037822872, -0.04098917, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.76348066, 0.4857204, 0.4151668, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [-0.2599106, -1.0346818, 0.022682864, -0.0, 0.037822872, -0.04098917, -0.0], "split_indices": [24, 18, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [16.043537, 7.477901, 8.565637, 3.4313693, 4.046532, 4.6093073, 3.9563293], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.12897156, 0.051035427, -0.040008772, 0.060444143, -0.02085672], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.1642717, 1.7370255, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [-0.14197576, 0.4219407, -0.040008772, 0.060444143, -0.02085672], "split_indices": [2, 14, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.285967, 7.457727, 7.8282394, 3.1871822, 4.2705445], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.19000523, -0.0, 0.045297854, -0.02670874, 0.016323196], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.2269137, 0.54943675, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.0005570686, 0.915043, 0.045297854, -0.02670874, 0.016323196], "split_indices": [2, 16, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [17.047392, 8.892634, 8.154757, 4.7999616, 4.0926733], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.02069396, -0.19360027, 0.034295596, -0.04392765, -0.0, 0.010931377, -0.017501324], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.036528, 0.57237506, 0.0, 0.0, 0.2241499, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [0.93280274, -0.14122713, 0.034295596, -0.04392765, 0.3645176, 0.010931377, -0.017501324], "split_indices": [1, 3, 0, 0, 18, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [16.804298, 13.569745, 3.2345529, 4.796239, 8.773506, 4.66584, 4.107666], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.11762996, -0.0, -0.0416098, 0.046138696, -0.021034515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.80130446, 1.3246317, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.6000794, -0.14197576, -0.0416098, 0.046138696, -0.021034515], "split_indices": [16, 2, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.094675, 10.180282, 4.914393, 3.659319, 6.520963], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.13481258, -0.028304402, 0.053906728, 0.0001835185, -0.16616777, -0.0, -0.037297454], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, -1, -1, 5, -1, -1], "loss_changes": [1.6942046, 0.2137101, 0.0, 0.0, 0.45308208, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 4, 4], "right_children": [2, 4, -1, -1, 6, -1, -1], "split_conditions": [0.04118206, -0.14702597, 0.053906728, 0.0001835185, 0.4979429, -0.0, -0.037297454], "split_indices": [2, 40, 0, 0, 17, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [16.715643, 10.810069, 5.905573, 3.7281532, 7.081916, 3.2207427, 3.8611734], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.08828661, -0.34318942, 0.018948175, -0.0, -0.04557379], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.2872802, 0.21477258, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.513182, -0.6531407, 0.018948175, -0.0, -0.04557379], "split_indices": [11, 11, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [14.091598, 9.102762, 4.9888353, 3.3397112, 5.763051], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.117763974, -0.03261785, 0.006007276], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 19, "left_children": [1, -1, -1], "loss_changes": [0.87928593, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6405209, -0.03261785, 0.006007276], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [15.015263, 9.118219, 5.8970437], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.16112512, -0.041945074, 0.05769909, 0.0048726923, -0.029790998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, -1, -1, -1], "loss_changes": [2.1703024, 0.5453271, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.041871797, -0.54391986, 0.05769909, 0.0048726923, -0.029790998], "split_indices": [3, 19, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [18.07704, 11.1345005, 6.9425397, 5.9349694, 5.199531], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.16894296, 0.025290513, -0.030269971, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.7059885, 0.46962732, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.72108614, 0.5229938, 0.025290513, -0.030269971, -0.0], "split_indices": [8, 9, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.819355, 11.246306, 4.5730486, 7.940126, 3.3061807], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.13670243, -0.031567447, -0.0, -0.032141805, 0.030436078], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 22, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.42252705, 0.0, 0.9702847, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [-0.37289828, -0.031567447, -0.31478116, -0.032141805, 0.030436078], "split_indices": [18, 0, 14, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [14.002553, 6.053543, 7.94901, 3.558081, 4.3909287], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.114545114, -0.082667075, 0.060693033, -0.027122764, 0.014170557], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, -1, -1, -1], "loss_changes": [2.4121804, 0.7155519, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.04118206, 1.4032845, 0.060693033, -0.027122764, 0.014170557], "split_indices": [2, 10, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [17.92545, 11.94312, 5.98233, 8.797681, 3.1454394], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.02916095, 0.104276, -0.0, 0.03379779], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 24, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.6739301, 0.0, 0.578756, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [-0.38123915, -0.02916095, 0.022682864, -0.0, 0.03379779], "split_indices": [10, 0, 17, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [14.794952, 5.6247735, 9.170178, 4.135451, 5.034728], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.1363389, -0.035042327, -0.0, 0.033254653, -0.021845147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 25, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.65030074, 0.0, 0.8392333, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [-0.37289828, -0.035042327, -0.14197576, 0.033254653, -0.021845147], "split_indices": [18, 0, 2, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.625147, 6.8123894, 8.812757, 4.1416726, 4.671084], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.13556558, -0.0, 0.043682866, -0.17193547, 0.015647069, -0.0, -0.036585655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.0003124, 0.3637078, 0.0, 0.40559152, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [0.04118206, 0.915043, 0.043682866, -0.14702597, 0.015647069, -0.0, -0.036585655], "split_indices": [2, 16, 0, 40, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [17.171946, 11.179586, 5.9923587, 7.078994, 4.100592, 3.264528, 3.814466], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.044084217, 0.010311906, -0.23895204, -0.043729972, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 27, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.6088083, 0.0, 0.68314886, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [-0.25557253, 0.010311906, 0.644862, -0.043729972, -0.0], "split_indices": [39, 0, 4, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [14.629492, 5.678837, 8.950655, 5.5434575, 3.4071975], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.2180183, -0.039825477, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [0.7979553, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6405209, -0.039825477, -0.0], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [14.487583, 8.672412, 5.8151712], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.317972, -0.0, 0.06819138, -0.06300612, 0.03183962, 0.008236374, -0.036981124], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, -1, 5, -1, -1, -1], "loss_changes": [1.4679737, 0.46660748, 0.0, 0.69019026, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3], "right_children": [2, 4, -1, 6, -1, -1, -1], "split_conditions": [0.04118206, 0.915043, 0.06819138, -0.14702597, 0.03183962, 0.008236374, -0.036981124], "split_indices": [2, 16, 0, 40, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [16.608498, 10.622999, 5.985499, 7.347127, 3.2758718, 3.2699237, 4.0772033], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, -0.016497867, 0.20163967, 0.03952686, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 30, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.6011511, 0.0, 0.63312334, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [-0.38123915, -0.016497867, 1.4032845, 0.03952686, -0.0], "split_indices": [10, 0, 10, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.080726, 5.9436307, 9.1370945, 5.6903567, 3.446738], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.17945704, -0.0, -0.037019156, -0.010328874, 0.01573466], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.6766095, 0.17277485, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.056793895, -0.37289828, -0.037019156, -0.010328874, 0.01573466], "split_indices": [39, 18, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.370571, 7.586235, 7.784336, 3.5822215, 4.0040135], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.11338958, -0.13111156, 0.04992236, 0.011830892, -0.04712086], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, -1, -1], "loss_changes": [2.1030304, 1.2601378, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [-0.2599106, -1.0346818, 0.04992236, 0.011830892, -0.04712086], "split_indices": [24, 18, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [16.696838, 9.037939, 7.6589, 3.8939714, 5.1439676], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.23661877, -0.036138337, 0.034802206, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.617623, 0.18904167, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [-0.02877087, 0.9744821, -0.036138337, 0.034802206, -0.0], "split_indices": [18, 14, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.845835, 8.556507, 7.289328, 4.9783654, 3.5781412], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.016075239, 0.037824597, -0.031611424, -0.0057910993, 0.02432349], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.51553285, 0.39364076, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.25541353, -0.37289828, -0.031611424, -0.0057910993, 0.02432349], "split_indices": [7, 18, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [12.930589, 8.876945, 4.053644, 3.2240689, 5.6528754], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.08300262, -0.006165301, 0.038098905, 0.00642946, -0.021807624], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.8150035, 0.33329865, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.42700037, -0.54391986, 0.038098905, 0.00642946, -0.021807624], "split_indices": [0, 19, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.477877, 10.08388, 5.393996, 4.548986, 5.534895], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.0327868, 0.0006354776, -0.024467176, -0.00022860336, 0.021677285], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, -1, -1, -1], "loss_changes": [0.3422939, 0.22059368, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [-0.09211234, -0.18888856, -0.024467176, -0.00022860336, 0.021677285], "split_indices": [30, 35, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [12.852952, 7.886423, 4.9665294, 4.1924896, 3.6939335], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.042681433, 0.07503283, -0.25572115, 0.039941348, -0.0068172845, -0.0, -0.03696415], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [0.5973519, 0.72817177, 0.21241754, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [-0.14197576, 0.7960122, -0.42010975, 0.039941348, -0.0068172845, -0.0, -0.03696415], "split_indices": [2, 1, 9, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [14.84971, 6.9586015, 7.891109, 3.721887, 3.2367146, 3.0811958, 4.809913], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [0.15889294, 0.39923576, -0.07883689, 0.054693818, -0.0, -0.040368762, 0.0055133733], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, -1, -1, -1, -1], "loss_changes": [1.3534027, 0.581784, 0.6996337, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2], "right_children": [2, 4, 6, -1, -1, -1, -1], "split_conditions": [-0.09049508, 0.644862, 0.14290772, 0.054693818, -0.0, -0.040368762, 0.0055133733], "split_indices": [22, 4, 26, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0], "sum_hessian": [16.528667, 9.973068, 6.5555983, 6.791541, 3.1815274, 3.50581, 3.0497882], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "7", "size_leaf_vector": "1"}}, {"base_weights": [-0.0, 0.1517442, -0.038452897, -0.0, 0.0355309], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, -1, -1], "loss_changes": [1.1259727, 0.6763299, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1], "right_children": [2, 4, -1, -1, -1], "split_conditions": [0.2888852, -0.4198762, -0.038452897, -0.0, 0.0355309], "split_indices": [18, 11, 0, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [15.37512, 10.560398, 4.814722, 4.094541, 6.465857], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [-0.088989146, -0.03634192, -0.0, 0.033103835, -0.016522145], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0], "id": 40, "left_children": [1, -1, 3, -1, -1], "loss_changes": [0.63057214, 0.0, 0.6903987, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2], "right_children": [2, -1, 4, -1, -1], "split_conditions": [-0.14702597, -0.03634192, 0.54740113, 0.033103835, -0.016522145], "split_indices": [40, 0, 8, 0, 0], "split_type": [0, 0, 0, 0, 0], "sum_hessian": [12.767044, 4.599868, 8.167176, 4.0171657, 4.1500106], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "5", "size_leaf_vector": "1"}}, {"base_weights": [0.20200132, 0.03882663, -0.0], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 41, "left_children": [1, -1, -1], "loss_changes": [0.9148481, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.054014612, 0.03882663, -0.0], "split_indices": [33, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [15.9244795, 9.649658, 6.2748218], "tree_param": {"num_deleted": "0", "num_feature": "41", "num_nodes": "3", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "3", "num_feature": "41", "num_target": "1"}, "objective": {"name": "multi:softprob", "softmax_multiclass_param": {"num_class": "3"}}}, "version": [2, 1, 3]}