#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XGBoost模型验证和调优
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, log_loss
import warnings
warnings.filterwarnings('ignore')

def load_training_data():
    """加载训练数据"""
    print("📊 重新加载训练数据...")
    
    # 重新执行数据准备
    exec(open('xgboost_match_predictor.py').read(), globals())
    
    return match_features

def validate_model_with_different_params():
    """使用不同参数验证模型"""
    print("🔧 测试不同的XGBoost参数...")
    
    # 加载数据
    match_features = load_training_data()
    
    X = np.array([mf['features'] for mf in match_features])
    y = np.array([mf['label'] for mf in match_features])
    
    print(f"数据形状: {X.shape}")
    print(f"标签分布: {np.bincount(y)}")
    
    # 标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 不同的参数配置
    param_configs = [
        {
            'name': '强正则化',
            'params': {
                'objective': 'multi:softprob',
                'num_class': 3,
                'max_depth': 3,
                'learning_rate': 0.05,
                'subsample': 0.7,
                'colsample_bytree': 0.7,
                'reg_alpha': 10,
                'reg_lambda': 10,
                'min_child_weight': 5,
                'gamma': 1,
                'random_state': 42,
                'verbosity': 0
            }
        },
        {
            'name': '中等正则化',
            'params': {
                'objective': 'multi:softprob',
                'num_class': 3,
                'max_depth': 4,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 1,
                'reg_lambda': 1,
                'min_child_weight': 3,
                'gamma': 0.1,
                'random_state': 42,
                'verbosity': 0
            }
        },
        {
            'name': '轻度正则化',
            'params': {
                'objective': 'multi:softprob',
                'num_class': 3,
                'max_depth': 6,
                'learning_rate': 0.15,
                'subsample': 0.9,
                'colsample_bytree': 0.9,
                'reg_alpha': 0.1,
                'reg_lambda': 0.1,
                'min_child_weight': 1,
                'gamma': 0,
                'random_state': 42,
                'verbosity': 0
            }
        }
    ]
    
    best_config = None
    best_score = float('inf')
    
    for config in param_configs:
        print(f"\n🧪 测试配置: {config['name']}")
        
        # 创建DMatrix
        dtrain = xgb.DMatrix(X_scaled, label=y)
        
        # 交叉验证
        cv_results = xgb.cv(
            config['params'],
            dtrain,
            num_boost_round=100,
            nfold=5,
            stratified=True,
            shuffle=True,
            seed=42,
            early_stopping_rounds=10,
            verbose_eval=False
        )
        
        best_rounds = len(cv_results)
        cv_score = cv_results['test-mlogloss-mean'].iloc[-1]
        cv_std = cv_results['test-mlogloss-std'].iloc[-1]
        
        print(f"  最佳轮数: {best_rounds}")
        print(f"  CV分数: {cv_score:.4f} ± {cv_std:.4f}")
        
        # 训练模型并测试预测
        model = xgb.train(
            config['params'],
            dtrain,
            num_boost_round=best_rounds
        )
        
        # 测试预测概率
        test_probs = model.predict(dtrain)
        print(f"  预测概率范围: [{test_probs.min():.3f}, {test_probs.max():.3f}]")
        print(f"  预测概率标准差: {test_probs.std():.3f}")
        
        # 检查是否所有预测都相同
        unique_predictions = len(np.unique(test_probs.round(3), axis=0))
        print(f"  唯一预测数: {unique_predictions}")
        
        if cv_score < best_score:
            best_score = cv_score
            best_config = config
    
    print(f"\n🏆 最佳配置: {best_config['name']}")
    return best_config

def train_optimized_model(best_config):
    """训练优化后的模型"""
    print(f"\n🚀 使用最佳配置训练模型...")
    
    # 加载数据
    match_features = load_training_data()
    
    X = np.array([mf['features'] for mf in match_features])
    y = np.array([mf['label'] for mf in match_features])
    
    # 标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 创建DMatrix
    dtrain = xgb.DMatrix(X_train, label=y_train)
    dtest = xgb.DMatrix(X_test, label=y_test)
    
    # 训练模型
    model = xgb.train(
        best_config['params'],
        dtrain,
        num_boost_round=50,
        evals=[(dtrain, 'train'), (dtest, 'test')],
        early_stopping_rounds=10,
        verbose_eval=False
    )
    
    # 预测
    train_probs = model.predict(dtrain)
    test_probs = model.predict(dtest)
    
    train_preds = np.argmax(train_probs, axis=1)
    test_preds = np.argmax(test_probs, axis=1)
    
    # 评估
    train_acc = accuracy_score(y_train, train_preds)
    test_acc = accuracy_score(y_test, test_preds)
    
    print(f"\n📊 模型性能:")
    print(f"训练准确率: {train_acc:.3f}")
    print(f"测试准确率: {test_acc:.3f}")
    
    print(f"\n🎯 测试集详细报告:")
    print(classification_report(y_test, test_preds, target_names=['客胜', '平局', '主胜']))
    
    print(f"\n📈 预测概率分析:")
    print(f"训练集概率范围: [{train_probs.min():.3f}, {train_probs.max():.3f}]")
    print(f"测试集概率范围: [{test_probs.min():.3f}, {test_probs.max():.3f}]")
    print(f"测试集概率标准差: {test_probs.std():.3f}")
    
    # 检查预测多样性
    unique_train_preds = len(np.unique(train_probs.round(3), axis=0))
    unique_test_preds = len(np.unique(test_probs.round(3), axis=0))
    print(f"训练集唯一预测数: {unique_train_preds}")
    print(f"测试集唯一预测数: {unique_test_preds}")
    
    # 保存优化后的模型
    model.save_model('xgboost_optimized_model.json')
    print(f"\n💾 优化后模型已保存为 xgboost_optimized_model.json")
    
    return model, scaler

def analyze_feature_importance(model):
    """分析特征重要性"""
    print(f"\n📊 特征重要性分析:")
    
    importance = model.get_score(importance_type='weight')
    
    feature_names = [
        'home_elo', 'away_elo', 'elo_diff', 'elo_ratio',
        'home_points', 'away_points', 'points_diff', 'home_point_rate', 'away_point_rate',
        'home_goals_for', 'away_goals_for', 'home_goals_against', 'away_goals_against',
        'home_goal_diff', 'away_goal_diff', 'home_avg_goals_for', 'away_avg_goals_for',
        'home_avg_goals_against', 'away_avg_goals_against',
        'home_win_rate', 'away_win_rate', 'home_draw_rate', 'away_draw_rate',
        'home_loss_rate', 'away_loss_rate',
        'home_home_win_rate', 'away_away_win_rate', 'home_home_draw_rate', 'away_away_draw_rate',
        'home_home_loss_rate', 'away_away_loss_rate',
        'home_big_wins', 'away_big_wins', 'home_big_losses', 'away_big_losses',
        'home_clean_sheets', 'away_clean_sheets', 'home_failed_to_score', 'away_failed_to_score',
        'home_recent_form', 'away_recent_form'
    ]
    
    if importance:
        sorted_importance = sorted(importance.items(), key=lambda x: x[1], reverse=True)
        print("TOP 15 重要特征:")
        for i, (feature_idx, score) in enumerate(sorted_importance[:15]):
            if feature_idx.startswith('f'):
                idx = int(feature_idx[1:])
                if idx < len(feature_names):
                    feature_name = feature_names[idx]
                else:
                    feature_name = feature_idx
            else:
                feature_name = feature_idx
            print(f"  {i+1:2d}. {feature_name:20s}: {score:3d}")
    else:
        print("  无特征重要性信息 (可能模型过于简单)")

def test_prediction_diversity():
    """测试预测多样性"""
    print(f"\n🧪 测试预测多样性...")
    
    # 加载优化后的模型
    try:
        model = xgb.Booster()
        model.load_model('xgboost_optimized_model.json')
        
        # 创建一些测试样本
        test_samples = [
            # 强队vs弱队
            [1600, 1400, 200, 0.53] + [0] * 37,  # 强队主场
            [1400, 1600, -200, 0.47] + [0] * 37, # 弱队主场
            # 实力相近
            [1500, 1500, 0, 0.5] + [0] * 37,     # 实力相等
        ]
        
        dtest = xgb.DMatrix(test_samples)
        probs = model.predict(dtest)
        
        print("测试样本预测结果:")
        scenarios = ["强队主场", "弱队主场", "实力相等"]
        for i, (scenario, prob) in enumerate(zip(scenarios, probs)):
            print(f"  {scenario}: 主胜{prob[2]:.1%}, 平局{prob[1]:.1%}, 客胜{prob[0]:.1%}")
            
    except Exception as e:
        print(f"  模型加载失败: {e}")

def main():
    """主函数"""
    print("🔍 XGBoost模型验证和调优")
    print("="*40)
    
    # 验证不同参数
    best_config = validate_model_with_different_params()
    
    # 训练优化模型
    model, scaler = train_optimized_model(best_config)
    
    # 分析特征重要性
    analyze_feature_importance(model)
    
    # 测试预测多样性
    test_prediction_diversity()
    
    print(f"\n✅ 模型验证和调优完成!")
    print(f"💡 建议使用 {best_config['name']} 配置")

if __name__ == "__main__":
    main()
