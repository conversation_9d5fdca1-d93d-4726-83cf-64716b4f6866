#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于新的正确数据重新运行验证测试
"""

import pandas as pd
import numpy as np
import random
import math
from collections import defaultdict

def load_updated_data():
    """加载更新后的数据"""
    df_stats = pd.read_csv('team_stats_for_prediction_updated.csv', encoding='utf-8')
    df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
    df_schedule = pd.read_csv('maches2_utf8.csv', encoding='utf-8')
    return df_stats, df_results, df_schedule

def calculate_team_strength_at_round(df_results, target_round):
    """计算指定轮次后的队伍实力"""
    print(f"📊 计算第{target_round}轮后的队伍实力...")
    
    # 获取目标轮次之前的比赛
    target_rounds = [f'第{i}轮' for i in range(1, target_round + 1)]
    # 包含补赛
    if target_round >= 4:
        target_rounds.append('第4轮(补赛)')
    
    completed_matches = df_results[df_results['轮次'].isin(target_rounds)]
    
    # 统计各队数据
    teams = set()
    team_stats = {}
    
    for _, row in completed_matches.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
            teams.add(row['参赛队伍1'])
            teams.add(row['参赛队伍2'])
    
    # 初始化统计
    for team in teams:
        team_stats[team] = {
            '已赛场次': 0, '胜场': 0, '平场': 0, '负场': 0,
            '进球数': 0, '失球数': 0, '积分': 0
        }
    
    # 统计比赛结果
    for _, row in completed_matches.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
            # 处理进球数列名
            goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
            goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
            
            if pd.notna(row[goals1_col]):
                team1, team2 = row['参赛队伍1'], row['参赛队伍2']
                goals1, goals2 = int(row[goals1_col]), int(row[goals2_col])
                
                team_stats[team1]['已赛场次'] += 1
                team_stats[team2]['已赛场次'] += 1
                team_stats[team1]['进球数'] += goals1
                team_stats[team1]['失球数'] += goals2
                team_stats[team2]['进球数'] += goals2
                team_stats[team2]['失球数'] += goals1
                
                if goals1 > goals2:
                    team_stats[team1]['胜场'] += 1
                    team_stats[team1]['积分'] += 3
                    team_stats[team2]['负场'] += 1
                elif goals1 < goals2:
                    team_stats[team2]['胜场'] += 1
                    team_stats[team2]['积分'] += 3
                    team_stats[team1]['负场'] += 1
                else:
                    team_stats[team1]['平场'] += 1
                    team_stats[team1]['积分'] += 1
                    team_stats[team2]['平场'] += 1
                    team_stats[team2]['积分'] += 1
    
    # 计算实力评分
    strengths = {}
    for team in teams:
        stats = team_stats[team]
        if stats['已赛场次'] > 0:
            # 基础实力指标 (权重: 40%)
            points_score = (stats['积分'] / (stats['已赛场次'] * 3)) * 40
            
            # 攻防平衡 (权重: 25%)
            net_goals = stats['进球数'] - stats['失球数']
            goal_diff_score = min(max((net_goals + 10) / 20, 0), 1) * 25
            
            # 状态趋势 (权重: 20%)
            win_rate = stats['胜场'] / stats['已赛场次']
            win_rate_score = win_rate * 20
            
            # 稳定性指标 (权重: 15%)
            stability_score = min(stats['已赛场次'] / 8, 1) * 15
            
            total_strength = points_score + goal_diff_score + win_rate_score + stability_score
            strengths[team] = total_strength
        else:
            strengths[team] = 50  # 默认中等实力
    
    return strengths, team_stats

def calculate_match_probabilities(team_a, team_b, strength_a, strength_b, is_home_a=True):
    """计算两队对阵的胜平负概率"""
    # 主场优势 (+3分实力)
    home_advantage = 3 if is_home_a else -3
    strength_diff = strength_a - strength_b + home_advantage
    
    # 使用sigmoid函数计算胜率
    win_prob = 1 / (1 + math.exp(-strength_diff / 15))
    
    # 平局概率与实力差距成反比
    draw_prob = 0.3 * math.exp(-(strength_diff ** 2) / 400)
    
    # 失败概率
    loss_prob = 1 - win_prob - draw_prob
    
    # 确保概率和为1
    total = win_prob + draw_prob + loss_prob
    return win_prob/total, draw_prob/total, loss_prob/total

def predict_next_round_results(team_strengths, next_round_matches):
    """预测下一轮比赛结果"""
    predictions = []
    
    for home_team, away_team in next_round_matches:
        if home_team in team_strengths and away_team in team_strengths:
            win_prob, draw_prob, loss_prob = calculate_match_probabilities(
                home_team, away_team,
                team_strengths[home_team], team_strengths[away_team],
                is_home_a=True
            )
            
            # 预测最可能的结果
            if win_prob > draw_prob and win_prob > loss_prob:
                predicted_result = "主胜"
                confidence = win_prob
            elif draw_prob > loss_prob:
                predicted_result = "平局"
                confidence = draw_prob
            else:
                predicted_result = "客胜"
                confidence = loss_prob
            
            predictions.append({
                'home_team': home_team,
                'away_team': away_team,
                'predicted_result': predicted_result,
                'confidence': confidence,
                'probabilities': (win_prob, draw_prob, loss_prob)
            })
    
    return predictions

def get_actual_results(df_results, target_round):
    """获取指定轮次的实际比赛结果"""
    round_name = f'第{target_round}轮'
    round_matches = df_results[df_results['轮次'] == round_name]
    
    actual_results = []
    for _, row in round_matches.iterrows():
        goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
        goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
        
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']) and pd.notna(row[goals1_col]):
            home_team, away_team = row['参赛队伍1'], row['参赛队伍2']
            goals1, goals2 = int(row[goals1_col]), int(row[goals2_col])
            
            if goals1 > goals2:
                actual_result = "主胜"
            elif goals1 < goals2:
                actual_result = "客胜"
            else:
                actual_result = "平局"
            
            actual_results.append({
                'home_team': home_team,
                'away_team': away_team,
                'actual_result': actual_result,
                'score': f'{goals1}-{goals2}'
            })
    
    return actual_results

def get_round_matches_from_schedule(df_schedule, target_round):
    """从赛程表获取指定轮次的对阵"""
    round_name = f'第{target_round}轮'
    round_schedule = df_schedule[df_schedule['Round（轮次）'] == round_name]
    
    matches = []
    for _, row in round_schedule.iterrows():
        home_team = row['HomeTeam（主队）'].replace('市', '队')
        away_team = row['AwayTeam（客队）'].replace('市', '队')
        matches.append((home_team, away_team))
    
    return matches

def validate_predictions(predictions, actual_results):
    """验证预测准确率"""
    correct_predictions = 0
    total_predictions = 0
    
    # 创建实际结果字典
    actual_dict = {}
    for result in actual_results:
        key = (result['home_team'], result['away_team'])
        actual_dict[key] = result['actual_result']
    
    print("\n📋 预测结果对比:")
    print("主队      vs 客队      预测结果  实际结果  置信度   正确性")
    print("-" * 65)
    
    for pred in predictions:
        key = (pred['home_team'], pred['away_team'])
        if key in actual_dict:
            actual = actual_dict[key]
            predicted = pred['predicted_result']
            confidence = pred['confidence']
            
            is_correct = predicted == actual
            if is_correct:
                correct_predictions += 1
            total_predictions += 1
            
            status = "✅" if is_correct else "❌"
            print(f"{pred['home_team']:8s} vs {pred['away_team']:8s}  {predicted:4s}    {actual:4s}    {confidence:.1%}   {status}")
    
    accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0
    return accuracy, correct_predictions, total_predictions

def run_updated_validation():
    """运行更新后的验证测试"""
    print("🔄 基于新数据的验证测试")
    print("="*50)
    
    # 加载数据
    df_stats, df_results, df_schedule = load_updated_data()
    
    print("📊 当前积分榜 TOP 5:")
    for i, (_, row) in enumerate(df_stats.head().iterrows(), 1):
        print(f"  {i}. {row['队伍名称']}: {row['积分']}分 ({row['已赛场次']}场)")
    
    # 验证配置：用前几轮预测后几轮
    validation_configs = [
        (3, 4),  # 用前3轮预测第4轮
        (4, 5),  # 用前4轮预测第5轮
        (5, 6),  # 用前5轮预测第6轮
        (6, 7),  # 用前6轮预测第7轮
        (7, 8),  # 用前7轮预测第8轮
    ]
    
    all_accuracies = []
    
    for train_rounds, test_round in validation_configs:
        print(f"\n🎯 验证案例: 用前{train_rounds}轮数据预测第{test_round}轮")
        print("-" * 50)
        
        # 计算训练轮次后的实力
        team_strengths, team_stats = calculate_team_strength_at_round(df_results, train_rounds)
        
        print(f"  基于前{train_rounds}轮的实力排名 TOP 5:")
        sorted_teams = sorted(team_strengths.items(), key=lambda x: x[1], reverse=True)
        for i, (team, strength) in enumerate(sorted_teams[:5], 1):
            points = team_stats[team]['积分']
            games = team_stats[team]['已赛场次']
            print(f"    {i}. {team}: {strength:.1f}分 ({points}积分/{games}场)")
        
        # 获取测试轮次的对阵
        test_matches = get_round_matches_from_schedule(df_schedule, test_round)
        
        # 预测结果
        predictions = predict_next_round_results(team_strengths, test_matches)
        
        # 获取实际结果
        actual_results = get_actual_results(df_results, test_round)
        
        # 验证准确率
        accuracy, correct, total = validate_predictions(predictions, actual_results)
        all_accuracies.append(accuracy)
        
        print(f"\n  📊 第{test_round}轮预测准确率: {accuracy:.1%} ({correct}/{total})")
    
    # 总体准确率
    overall_accuracy = np.mean(all_accuracies)
    print(f"\n🏆 基于新数据的总体预测准确率: {overall_accuracy:.1%}")
    print(f"📈 各轮准确率: {[f'{acc:.1%}' for acc in all_accuracies]}")
    
    return overall_accuracy, all_accuracies

def main():
    """主函数"""
    overall_accuracy, round_accuracies = run_updated_validation()
    
    print(f"\n🎯 基于新数据的验证结论:")
    print(f"  • 更新后的预测准确率为 {overall_accuracy:.1%}")
    print(f"  • 这是基于正确数据的真实验证结果")
    print(f"  • 验证了原始方法在新数据上的有效性")

if __name__ == "__main__":
    main()
