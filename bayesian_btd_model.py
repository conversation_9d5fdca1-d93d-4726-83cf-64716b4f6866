#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
贝叶斯 <PERSON> (BTD) 模型
专门处理小样本下的胜负平三态预测
"""

import pandas as pd
import numpy as np
import pymc as pm
import arviz as az
from scipy import stats
from sklearn.metrics import accuracy_score, classification_report, log_loss
import warnings
warnings.filterwarnings('ignore')

class BayesianBTDModel:
    """贝叶斯 <PERSON> 模型"""
    
    def __init__(self):
        self.model = None
        self.trace = None
        self.teams = []
        self.team_to_idx = {}
        self.idx_to_team = {}
        
    def load_and_prepare_data(self):
        """加载并准备数据"""
        print("📊 加载比赛数据...")
        
        df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
        
        # 清理数据
        matches = []
        teams = set()
        
        for _, row in df_results.iterrows():
            if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
                goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
                goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
                
                if pd.notna(row[goals1_col]) and pd.notna(row[goals2_col]):
                    home_team = row['参赛队伍1']
                    away_team = row['参赛队伍2']
                    home_goals = int(row[goals1_col])
                    away_goals = int(row[goals2_col])
                    
                    teams.add(home_team)
                    teams.add(away_team)
                    
                    # 确定结果
                    if home_goals > away_goals:
                        result = 1  # 主胜
                    elif home_goals < away_goals:
                        result = -1  # 主负
                    else:
                        result = 0  # 平局
                    
                    matches.append({
                        'home_team': home_team,
                        'away_team': away_team,
                        'home_goals': home_goals,
                        'away_goals': away_goals,
                        'result': result
                    })
        
        # 建立队伍索引
        self.teams = sorted(list(teams))
        self.team_to_idx = {team: i for i, team in enumerate(self.teams)}
        self.idx_to_team = {i: team for i, team in enumerate(self.teams)}
        
        print(f"队伍数量: {len(self.teams)}")
        print(f"比赛数量: {len(matches)}")
        print(f"结果分布: 主胜{sum(1 for m in matches if m['result']==1)}, "
              f"平局{sum(1 for m in matches if m['result']==0)}, "
              f"主负{sum(1 for m in matches if m['result']==-1)}")
        
        return matches
    
    def calculate_prior_strengths(self, matches):
        """计算先验强度参数"""
        print("🧮 计算先验强度参数...")
        
        # 基于历史胜率计算先验
        team_stats = {team: {'wins': 0, 'draws': 0, 'losses': 0, 'games': 0} for team in self.teams}
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            result = match['result']
            
            team_stats[home_team]['games'] += 1
            team_stats[away_team]['games'] += 1
            
            if result == 1:  # 主胜
                team_stats[home_team]['wins'] += 1
                team_stats[away_team]['losses'] += 1
            elif result == -1:  # 主负
                team_stats[away_team]['wins'] += 1
                team_stats[home_team]['losses'] += 1
            else:  # 平局
                team_stats[home_team]['draws'] += 1
                team_stats[away_team]['draws'] += 1
        
        # 计算每队的胜率作为先验强度
        prior_strengths = []
        for team in self.teams:
            stats = team_stats[team]
            if stats['games'] > 0:
                win_rate = stats['wins'] / stats['games']
                # 转换为强度参数 (避免0值)
                strength = max(win_rate * 2 + 0.1, 0.1)  # 映射到[0.1, 2.1]
            else:
                strength = 1.0  # 默认中等强度
            prior_strengths.append(strength)
        
        print(f"先验强度范围: [{min(prior_strengths):.2f}, {max(prior_strengths):.2f}]")
        return np.array(prior_strengths)
    
    def build_bayesian_model(self, matches, prior_strengths):
        """构建贝叶斯BTD模型"""
        print("🏗️ 构建贝叶斯BTD模型...")
        
        n_teams = len(self.teams)
        n_matches = len(matches)
        
        # 准备数据
        home_teams = np.array([self.team_to_idx[m['home_team']] for m in matches])
        away_teams = np.array([self.team_to_idx[m['away_team']] for m in matches])
        results = np.array([m['result'] for m in matches])
        
        with pm.Model() as model:
            # 队伍强度参数 α_i (对数正态分布)
            log_alpha = pm.Normal('log_alpha', 
                                mu=np.log(prior_strengths), 
                                sigma=0.5,  # 适度的不确定性
                                shape=n_teams)
            alpha = pm.Deterministic('alpha', pm.math.exp(log_alpha))
            
            # 平局参数 γ (伽马分布)
            gamma = pm.Gamma('gamma', alpha=2, beta=2)  # 先验期望为1
            
            # 主场优势参数
            home_advantage = pm.Normal('home_advantage', mu=0.1, sigma=0.1)
            
            # 计算比赛概率
            alpha_home = alpha[home_teams] * pm.math.exp(home_advantage)
            alpha_away = alpha[away_teams]
            
            # BTD模型概率
            total = alpha_home + alpha_away + gamma
            
            p_home_win = alpha_home / total
            p_draw = gamma / total
            p_away_win = alpha_away / total
            
            # 使用分类分布建模
            # 将结果转换为分类标签: -1->0, 0->1, 1->2
            categorical_results = np.where(results == -1, 0, np.where(results == 0, 1, 2))

            # 构建概率矩阵
            probs_matrix = pm.math.stack([p_away_win, p_draw, p_home_win], axis=1)

            # 分类似然
            likelihood = pm.Categorical('likelihood', p=probs_matrix, observed=categorical_results)
        
        self.model = model
        return model
    
    def fit_model(self, matches, prior_strengths, n_samples=2000, n_tune=1000):
        """拟合模型"""
        print("🔥 使用MCMC拟合模型...")
        
        model = self.build_bayesian_model(matches, prior_strengths)
        
        with model:
            # MCMC采样
            self.trace = pm.sample(
                draws=n_samples,
                tune=n_tune,
                chains=2,
                cores=1,
                random_seed=42,
                return_inferencedata=True,
                progressbar=True
            )
        
        print("✅ MCMC采样完成")
        return self.trace
    
    def predict_match(self, home_team, away_team):
        """预测单场比赛"""
        if self.trace is None:
            raise ValueError("模型尚未训练")
        
        home_idx = self.team_to_idx[home_team]
        away_idx = self.team_to_idx[away_team]
        
        # 从后验分布中采样
        alpha_samples = self.trace.posterior['alpha'].values.reshape(-1, len(self.teams))
        gamma_samples = self.trace.posterior['gamma'].values.flatten()
        home_adv_samples = self.trace.posterior['home_advantage'].values.flatten()
        
        # 计算预测概率
        alpha_home = alpha_samples[:, home_idx] * np.exp(home_adv_samples)
        alpha_away = alpha_samples[:, away_idx]
        total = alpha_home + alpha_away + gamma_samples
        
        p_home_win = np.mean(alpha_home / total)
        p_draw = np.mean(gamma_samples / total)
        p_away_win = np.mean(alpha_away / total)
        
        # 归一化
        total_prob = p_home_win + p_draw + p_away_win
        p_home_win /= total_prob
        p_draw /= total_prob
        p_away_win /= total_prob
        
        return {
            'p_home_win': p_home_win,
            'p_draw': p_draw,
            'p_away_win': p_away_win,
            'predicted_result': np.argmax([p_away_win, p_draw, p_home_win])  # 0:客胜, 1:平局, 2:主胜
        }
    
    def evaluate_model(self, matches):
        """评估模型性能"""
        print("📊 评估模型性能...")
        
        predictions = []
        actuals = []
        probabilities = []
        
        for match in matches:
            home_team = match['home_team']
            away_team = match['away_team']
            actual_result = match['result']
            
            # 预测
            pred = self.predict_match(home_team, away_team)
            
            predictions.append(pred['predicted_result'])
            probabilities.append([pred['p_away_win'], pred['p_draw'], pred['p_home_win']])
            
            # 转换实际结果
            if actual_result == 1:
                actuals.append(2)  # 主胜
            elif actual_result == -1:
                actuals.append(0)  # 主负
            else:
                actuals.append(1)  # 平局
        
        # 计算准确率
        accuracy = accuracy_score(actuals, predictions)
        
        # 计算Brier分数
        probabilities = np.array(probabilities)
        actual_probs = np.zeros_like(probabilities)
        for i, actual in enumerate(actuals):
            actual_probs[i, actual] = 1
        
        brier_score = np.mean(np.sum((probabilities - actual_probs)**2, axis=1))
        
        print(f"准确率: {accuracy:.3f}")
        print(f"Brier分数: {brier_score:.3f}")
        
        print("\n分类报告:")
        print(classification_report(actuals, predictions, target_names=['客胜', '平局', '主胜']))
        
        return accuracy, brier_score
    
    def get_team_strengths(self):
        """获取队伍强度排名"""
        if self.trace is None:
            raise ValueError("模型尚未训练")
        
        alpha_samples = self.trace.posterior['alpha'].values.reshape(-1, len(self.teams))
        mean_strengths = np.mean(alpha_samples, axis=0)
        std_strengths = np.std(alpha_samples, axis=0)
        
        team_strengths = []
        for i, team in enumerate(self.teams):
            team_strengths.append({
                'team': team,
                'strength_mean': mean_strengths[i],
                'strength_std': std_strengths[i]
            })
        
        # 按强度排序
        team_strengths.sort(key=lambda x: x['strength_mean'], reverse=True)
        
        print("\n🏆 队伍强度排名:")
        print("排名  队伍      强度均值  强度标准差")
        print("-" * 35)
        for i, ts in enumerate(team_strengths, 1):
            print(f"{i:2d}   {ts['team']:8s}  {ts['strength_mean']:.3f}    {ts['strength_std']:.3f}")
        
        return team_strengths
    
    def run_btd_analysis(self):
        """运行完整的BTD分析"""
        print("🎯 贝叶斯Bradley-Terry-Davidson模型分析")
        print("="*50)
        
        # 1. 加载数据
        matches = self.load_and_prepare_data()
        
        # 2. 计算先验
        prior_strengths = self.calculate_prior_strengths(matches)
        
        # 3. 拟合模型
        trace = self.fit_model(matches, prior_strengths)
        
        # 4. 评估性能
        accuracy, brier_score = self.evaluate_model(matches)
        
        # 5. 获取队伍强度
        team_strengths = self.get_team_strengths()
        
        # 6. 显示关键参数
        gamma_mean = np.mean(self.trace.posterior['gamma'].values)
        home_adv_mean = np.mean(self.trace.posterior['home_advantage'].values)
        
        print(f"\n📈 模型参数:")
        print(f"平局参数 γ: {gamma_mean:.3f}")
        print(f"主场优势: {home_adv_mean:.3f}")
        
        print(f"\n✅ BTD模型分析完成!")
        print(f"🎯 准确率: {accuracy:.1%}")
        print(f"📊 Brier分数: {brier_score:.3f}")
        
        return accuracy, brier_score, team_strengths

def main():
    """主函数"""
    try:
        model = BayesianBTDModel()
        accuracy, brier_score, team_strengths = model.run_btd_analysis()
        
        print(f"\n💡 BTD模型特点:")
        print(f"✅ 专门处理小样本三态预测")
        print(f"✅ 通过先验信息弥补数据不足")
        print(f"✅ MCMC采样获得不确定性量化")
        print(f"✅ 平局参数γ专门建模平局概率")
        
    except Exception as e:
        print(f"❌ 模型运行出错: {e}")
        print("请确保已安装 pymc 库: pip install pymc")

if __name__ == "__main__":
    main()
