#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门计算预测"输"的准确率分析
"""

import pandas as pd
import numpy as np
import random
import math
from collections import defaultdict

def load_updated_data():
    """加载更新后的数据"""
    df_stats = pd.read_csv('team_stats_for_prediction_updated.csv', encoding='utf-8')
    df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
    df_schedule = pd.read_csv('maches2_utf8.csv', encoding='utf-8')
    return df_stats, df_results, df_schedule

def calculate_team_strength_at_round(df_results, target_round):
    """计算指定轮次后的队伍实力"""
    # 获取目标轮次之前的比赛
    target_rounds = [f'第{i}轮' for i in range(1, target_round + 1)]
    if target_round >= 4:
        target_rounds.append('第4轮(补赛)')
    
    completed_matches = df_results[df_results['轮次'].isin(target_rounds)]
    
    # 统计各队数据
    teams = set()
    team_stats = {}
    
    for _, row in completed_matches.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
            teams.add(row['参赛队伍1'])
            teams.add(row['参赛队伍2'])
    
    # 初始化统计
    for team in teams:
        team_stats[team] = {
            '已赛场次': 0, '胜场': 0, '平场': 0, '负场': 0,
            '进球数': 0, '失球数': 0, '积分': 0
        }
    
    # 统计比赛结果
    for _, row in completed_matches.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
            goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
            goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
            
            if pd.notna(row[goals1_col]):
                team1, team2 = row['参赛队伍1'], row['参赛队伍2']
                goals1, goals2 = int(row[goals1_col]), int(row[goals2_col])
                
                team_stats[team1]['已赛场次'] += 1
                team_stats[team2]['已赛场次'] += 1
                team_stats[team1]['进球数'] += goals1
                team_stats[team1]['失球数'] += goals2
                team_stats[team2]['进球数'] += goals2
                team_stats[team2]['失球数'] += goals1
                
                if goals1 > goals2:
                    team_stats[team1]['胜场'] += 1
                    team_stats[team1]['积分'] += 3
                    team_stats[team2]['负场'] += 1
                elif goals1 < goals2:
                    team_stats[team2]['胜场'] += 1
                    team_stats[team2]['积分'] += 3
                    team_stats[team1]['负场'] += 1
                else:
                    team_stats[team1]['平场'] += 1
                    team_stats[team1]['积分'] += 1
                    team_stats[team2]['平场'] += 1
                    team_stats[team2]['积分'] += 1
    
    # 计算实力评分
    strengths = {}
    for team in teams:
        stats = team_stats[team]
        if stats['已赛场次'] > 0:
            points_score = (stats['积分'] / (stats['已赛场次'] * 3)) * 40
            net_goals = stats['进球数'] - stats['失球数']
            goal_diff_score = min(max((net_goals + 10) / 20, 0), 1) * 25
            win_rate = stats['胜场'] / stats['已赛场次']
            win_rate_score = win_rate * 20
            stability_score = min(stats['已赛场次'] / 8, 1) * 15
            
            total_strength = points_score + goal_diff_score + win_rate_score + stability_score
            strengths[team] = total_strength
        else:
            strengths[team] = 50
    
    return strengths, team_stats

def calculate_match_probabilities(team_a, team_b, strength_a, strength_b, is_home_a=True):
    """计算两队对阵的胜平负概率"""
    home_advantage = 3 if is_home_a else -3
    strength_diff = strength_a - strength_b + home_advantage
    
    win_prob = 1 / (1 + math.exp(-strength_diff / 15))
    draw_prob = 0.3 * math.exp(-(strength_diff ** 2) / 400)
    loss_prob = 1 - win_prob - draw_prob
    
    total = win_prob + draw_prob + loss_prob
    return win_prob/total, draw_prob/total, loss_prob/total

def predict_next_round_results(team_strengths, next_round_matches):
    """预测下一轮比赛结果"""
    predictions = []
    
    for home_team, away_team in next_round_matches:
        if home_team in team_strengths and away_team in team_strengths:
            win_prob, draw_prob, loss_prob = calculate_match_probabilities(
                home_team, away_team,
                team_strengths[home_team], team_strengths[away_team],
                is_home_a=True
            )
            
            # 预测最可能的结果
            if win_prob > draw_prob and win_prob > loss_prob:
                predicted_result = "主胜"
                confidence = win_prob
            elif draw_prob > loss_prob:
                predicted_result = "平局"
                confidence = draw_prob
            else:
                predicted_result = "客胜"
                confidence = loss_prob
            
            predictions.append({
                'home_team': home_team,
                'away_team': away_team,
                'predicted_result': predicted_result,
                'confidence': confidence,
                'probabilities': (win_prob, draw_prob, loss_prob)
            })
    
    return predictions

def get_actual_results(df_results, target_round):
    """获取指定轮次的实际比赛结果"""
    round_name = f'第{target_round}轮'
    round_matches = df_results[df_results['轮次'] == round_name]
    
    actual_results = []
    for _, row in round_matches.iterrows():
        goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
        goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
        
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']) and pd.notna(row[goals1_col]):
            home_team, away_team = row['参赛队伍1'], row['参赛队伍2']
            goals1, goals2 = int(row[goals1_col]), int(row[goals2_col])
            
            if goals1 > goals2:
                actual_result = "主胜"
            elif goals1 < goals2:
                actual_result = "客胜"
            else:
                actual_result = "平局"
            
            actual_results.append({
                'home_team': home_team,
                'away_team': away_team,
                'actual_result': actual_result,
                'score': f'{goals1}-{goals2}'
            })
    
    return actual_results

def get_round_matches_from_schedule(df_schedule, target_round):
    """从赛程表获取指定轮次的对阵"""
    round_name = f'第{target_round}轮'
    round_schedule = df_schedule[df_schedule['Round（轮次）'] == round_name]
    
    matches = []
    for _, row in round_schedule.iterrows():
        home_team = row['HomeTeam（主队）'].replace('市', '队')
        away_team = row['AwayTeam（客队）'].replace('市', '队')
        matches.append((home_team, away_team))
    
    return matches

def analyze_loss_predictions(predictions, actual_results):
    """专门分析预测"输"的准确率"""

    # 创建实际结果字典
    actual_dict = {}
    for result in actual_results:
        key = (result['home_team'], result['away_team'])
        actual_dict[key] = result['actual_result']

    # 分析统计
    stats = {
        'predicted_home_loss': {'correct': 0, 'total': 0},  # 预测主队输
        'predicted_away_loss': {'correct': 0, 'total': 0},  # 预测客队输
        'actual_home_loss': {'predicted_correct': 0, 'total': 0},  # 实际主队输
        'actual_away_loss': {'predicted_correct': 0, 'total': 0},  # 实际客队输
        'overall_loss': {'correct': 0, 'total': 0}  # 总体输的预测
    }
    
    detailed_results = []
    
    for pred in predictions:
        key = (pred['home_team'], pred['away_team'])
        if key in actual_dict:
            actual = actual_dict[key]
            predicted = pred['predicted_result']
            confidence = pred['confidence']
            
            # 记录详细结果
            detailed_results.append({
                'home_team': pred['home_team'],
                'away_team': pred['away_team'],
                'predicted': predicted,
                'actual': actual,
                'confidence': confidence,
                'home_loss_predicted': predicted == "客胜",
                'away_loss_predicted': predicted == "主胜",
                'home_loss_actual': actual == "客胜",
                'away_loss_actual': actual == "主胜"
            })
            
            # 统计预测主队输的情况
            if predicted == "客胜":
                stats['predicted_home_loss']['total'] += 1
                if actual == "客胜":
                    stats['predicted_home_loss']['correct'] += 1
            
            # 统计预测客队输的情况
            if predicted == "主胜":
                stats['predicted_away_loss']['total'] += 1
                if actual == "主胜":
                    stats['predicted_away_loss']['correct'] += 1
            
            # 统计实际主队输的情况
            if actual == "客胜":
                stats['actual_home_loss']['total'] += 1
                if predicted == "客胜":
                    stats['actual_home_loss']['predicted_correct'] += 1
            
            # 统计实际客队输的情况
            if actual == "主胜":
                stats['actual_away_loss']['total'] += 1
                if predicted == "主胜":
                    stats['actual_away_loss']['predicted_correct'] += 1
            
            # 统计总体"输"的预测
            if predicted in ["主胜", "客胜"] and actual in ["主胜", "客胜"]:
                stats['overall_loss']['total'] += 1
                if predicted == actual:
                    stats['overall_loss']['correct'] += 1
    
    return stats, detailed_results

def print_loss_analysis(stats, detailed_results):
    """打印输的预测分析结果"""
    
    print("\n📊 预测'输'的准确率分析")
    print("="*50)
    
    # 总体统计
    print("\n🎯 总体统计:")
    if stats['predicted_home_loss']['total'] > 0:
        home_loss_acc = stats['predicted_home_loss']['correct'] / stats['predicted_home_loss']['total']
        print(f"  预测主队输的准确率: {home_loss_acc:.1%} ({stats['predicted_home_loss']['correct']}/{stats['predicted_home_loss']['total']})")
    
    if stats['predicted_away_loss']['total'] > 0:
        away_loss_acc = stats['predicted_away_loss']['correct'] / stats['predicted_away_loss']['total']
        print(f"  预测客队输的准确率: {away_loss_acc:.1%} ({stats['predicted_away_loss']['correct']}/{stats['predicted_away_loss']['total']})")
    
    if stats['overall_loss']['total'] > 0:
        overall_loss_acc = stats['overall_loss']['correct'] / stats['overall_loss']['total']
        print(f"  总体预测输的准确率: {overall_loss_acc:.1%} ({stats['overall_loss']['correct']}/{stats['overall_loss']['total']})")
    
    # 召回率分析
    print("\n🔍 召回率分析 (实际输的比赛中，有多少被正确预测):")
    if stats['actual_home_loss']['total'] > 0:
        home_loss_recall = stats['actual_home_loss']['predicted_correct'] / stats['actual_home_loss']['total']
        print(f"  主队实际输的召回率: {home_loss_recall:.1%} ({stats['actual_home_loss']['predicted_correct']}/{stats['actual_home_loss']['total']})")
    
    if stats['actual_away_loss']['total'] > 0:
        away_loss_recall = stats['actual_away_loss']['predicted_correct'] / stats['actual_away_loss']['total']
        print(f"  客队实际输的召回率: {away_loss_recall:.1%} ({stats['actual_away_loss']['predicted_correct']}/{stats['actual_away_loss']['total']})")
    
    # 详细案例分析
    print("\n📋 详细案例分析:")
    print("主队      vs 客队      预测结果  实际结果  主队输预测  客队输预测  置信度")
    print("-" * 75)
    
    for result in detailed_results:
        home_loss_pred = "✓" if result['home_loss_predicted'] else "✗"
        away_loss_pred = "✓" if result['away_loss_predicted'] else "✗"
        
        print(f"{result['home_team']:8s} vs {result['away_team']:8s}  {result['predicted']:4s}    {result['actual']:4s}      {home_loss_pred:4s}       {away_loss_pred:4s}     {result['confidence']:.1%}")

def run_loss_prediction_analysis():
    """运行输的预测准确率分析"""
    print("🎯 专门分析预测'输'的准确率")
    print("="*50)
    
    # 加载数据
    df_stats, df_results, df_schedule = load_updated_data()
    
    # 验证配置
    validation_configs = [
        (3, 4), (4, 5), (5, 6), (6, 7), (7, 8)
    ]
    
    all_stats = {
        'predicted_home_loss': {'correct': 0, 'total': 0},
        'predicted_away_loss': {'correct': 0, 'total': 0},
        'actual_home_loss': {'predicted_correct': 0, 'total': 0},
        'actual_away_loss': {'predicted_correct': 0, 'total': 0},
        'overall_loss': {'correct': 0, 'total': 0}
    }
    
    all_detailed_results = []
    
    for train_rounds, test_round in validation_configs:
        print(f"\n🎯 第{test_round}轮输的预测分析:")
        print("-" * 30)
        
        # 计算实力
        team_strengths, team_stats = calculate_team_strength_at_round(df_results, train_rounds)
        
        # 获取对阵和预测
        test_matches = get_round_matches_from_schedule(df_schedule, test_round)
        predictions = predict_next_round_results(team_strengths, test_matches)
        actual_results = get_actual_results(df_results, test_round)
        
        # 分析输的预测
        round_stats, round_detailed = analyze_loss_predictions(predictions, actual_results)
        
        # 累计统计
        for key in all_stats:
            if key in round_stats:
                all_stats[key]['correct'] += round_stats[key]['correct']
                all_stats[key]['total'] += round_stats[key]['total']
        
        all_detailed_results.extend(round_detailed)
        
        # 打印本轮结果
        if round_stats['overall_loss']['total'] > 0:
            round_acc = round_stats['overall_loss']['correct'] / round_stats['overall_loss']['total']
            print(f"  第{test_round}轮预测输的准确率: {round_acc:.1%}")
    
    # 打印总体分析
    print_loss_analysis(all_stats, all_detailed_results)
    
    # 总结
    if all_stats['overall_loss']['total'] > 0:
        total_loss_acc = all_stats['overall_loss']['correct'] / all_stats['overall_loss']['total']
        print(f"\n🏆 总体预测'输'的准确率: {total_loss_acc:.1%}")
        print(f"📊 样本数: {all_stats['overall_loss']['total']}场有胜负的比赛")
        
        if total_loss_acc > 0.5:
            print("✅ 预测'输'的准确率超过50%，表现良好")
        else:
            print("⚠️ 预测'输'的准确率低于50%，需要改进")
    
    return all_stats

def main():
    """主函数"""
    stats = run_loss_prediction_analysis()

if __name__ == "__main__":
    main()
