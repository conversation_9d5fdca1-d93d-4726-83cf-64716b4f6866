#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的XGBoost预测系统
使用中等正则化参数，避免过拟合
"""

import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
import random
import warnings
warnings.filterwarnings('ignore')

class OptimizedXGBoostPredictor:
    """优化的XGBoost预测器"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = [
            'home_elo', 'away_elo', 'elo_diff', 'elo_ratio',
            'home_points', 'away_points', 'points_diff', 'home_point_rate', 'away_point_rate',
            'home_goals_for', 'away_goals_for', 'home_goals_against', 'away_goals_against',
            'home_goal_diff', 'away_goal_diff', 'home_avg_goals_for', 'away_avg_goals_for',
            'home_avg_goals_against', 'away_avg_goals_against',
            'home_win_rate', 'away_win_rate', 'home_draw_rate', 'away_draw_rate',
            'home_loss_rate', 'away_loss_rate',
            'home_home_win_rate', 'away_away_win_rate', 'home_home_draw_rate', 'away_away_draw_rate',
            'home_home_loss_rate', 'away_away_loss_rate',
            'home_big_wins', 'away_big_wins', 'home_big_losses', 'away_big_losses',
            'home_clean_sheets', 'away_clean_sheets', 'home_failed_to_score', 'away_failed_to_score',
            'home_recent_form', 'away_recent_form'
        ]
        
        # 优化后的参数 (中等正则化)
        self.params = {
            'objective': 'multi:softprob',
            'num_class': 3,
            'max_depth': 4,
            'learning_rate': 0.1,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 1,
            'reg_lambda': 1,
            'min_child_weight': 3,
            'gamma': 0.1,
            'random_state': 42,
            'verbosity': 0
        }
    
    def load_and_prepare_data(self):
        """加载并准备训练数据"""
        print("📊 加载比赛数据...")
        
        # 读取比赛结果
        df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
        
        # 清理数据
        valid_matches = []
        for _, row in df_results.iterrows():
            if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']):
                goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
                goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
                
                if pd.notna(row[goals1_col]) and pd.notna(row[goals2_col]):
                    valid_matches.append({
                        'round': row['轮次'],
                        'home_team': row['参赛队伍1'],
                        'away_team': row['参赛队伍2'],
                        'home_goals': int(row[goals1_col]),
                        'away_goals': int(row[goals2_col])
                    })
        
        print(f"有效比赛数据: {len(valid_matches)} 场")
        return valid_matches
    
    def build_features(self, matches):
        """构建特征"""
        print("🔢 构建特征数据...")
        
        # 获取所有队伍
        teams = set()
        for match in matches:
            teams.add(match['home_team'])
            teams.add(match['away_team'])
        
        teams = sorted(list(teams))
        
        # 初始化ELO和统计
        elo_ratings = {team: 1500 for team in teams}
        cumulative_stats = {}
        for team in teams:
            cumulative_stats[team] = {
                'games': 0, 'wins': 0, 'draws': 0, 'losses': 0,
                'goals_for': 0, 'goals_against': 0, 'points': 0,
                'home_games': 0, 'home_wins': 0, 'home_draws': 0, 'home_losses': 0,
                'away_games': 0, 'away_wins': 0, 'away_draws': 0, 'away_losses': 0,
                'recent_form': [], 'big_wins': 0, 'big_losses': 0,
                'clean_sheets': 0, 'failed_to_score': 0
            }
        
        # 按轮次排序
        matches_sorted = sorted(matches, key=lambda x: x['round'])
        
        # 构建特征
        X, y = [], []
        
        for match in matches_sorted:
            home_team = match['home_team']
            away_team = match['away_team']
            home_goals = match['home_goals']
            away_goals = match['away_goals']
            
            # 提取特征
            features = self.extract_features(home_team, away_team, elo_ratings, cumulative_stats)
            X.append(features)
            
            # 标签
            if home_goals > away_goals:
                y.append(2)  # 主胜
            elif home_goals < away_goals:
                y.append(0)  # 主负
            else:
                y.append(1)  # 平局
            
            # 更新ELO
            self.update_elo(elo_ratings, home_team, away_team, home_goals, away_goals)
            
            # 更新统计
            self.update_stats(cumulative_stats, home_team, away_team, home_goals, away_goals)
        
        return np.array(X), np.array(y), elo_ratings
    
    def extract_features(self, home_team, away_team, elo_ratings, cumulative_stats):
        """提取比赛特征"""
        features = []
        
        # ELO特征
        home_elo = elo_ratings[home_team]
        away_elo = elo_ratings[away_team]
        features.extend([
            home_elo, away_elo, home_elo - away_elo, home_elo / (home_elo + away_elo)
        ])
        
        # 统计特征
        home_stats = cumulative_stats[home_team]
        away_stats = cumulative_stats[away_team]
        
        home_games = max(home_stats['games'], 1)
        away_games = max(away_stats['games'], 1)
        
        features.extend([
            # 积分相关
            home_stats['points'], away_stats['points'], home_stats['points'] - away_stats['points'],
            home_stats['points'] / (home_games * 3), away_stats['points'] / (away_games * 3),
            
            # 进球相关
            home_stats['goals_for'], away_stats['goals_for'],
            home_stats['goals_against'], away_stats['goals_against'],
            home_stats['goals_for'] - home_stats['goals_against'],
            away_stats['goals_for'] - away_stats['goals_against'],
            home_stats['goals_for'] / home_games, away_stats['goals_for'] / away_games,
            home_stats['goals_against'] / home_games, away_stats['goals_against'] / away_games,
            
            # 胜率相关
            home_stats['wins'] / home_games, away_stats['wins'] / away_games,
            home_stats['draws'] / home_games, away_stats['draws'] / away_games,
            home_stats['losses'] / home_games, away_stats['losses'] / away_games,
        ])
        
        # 主客场表现
        home_home_games = max(home_stats['home_games'], 1)
        away_away_games = max(away_stats['away_games'], 1)
        
        features.extend([
            home_stats['home_wins'] / home_home_games, away_stats['away_wins'] / away_away_games,
            home_stats['home_draws'] / home_home_games, away_stats['away_draws'] / away_away_games,
            home_stats['home_losses'] / home_home_games, away_stats['away_losses'] / away_away_games,
        ])
        
        # 特殊指标
        features.extend([
            home_stats['big_wins'], away_stats['big_wins'],
            home_stats['big_losses'], away_stats['big_losses'],
            home_stats['clean_sheets'], away_stats['clean_sheets'],
            home_stats['failed_to_score'], away_stats['failed_to_score'],
        ])
        
        # 最近状态
        home_recent = home_stats['recent_form'][-3:] if len(home_stats['recent_form']) >= 3 else home_stats['recent_form']
        away_recent = away_stats['recent_form'][-3:] if len(away_stats['recent_form']) >= 3 else away_stats['recent_form']
        
        features.extend([
            sum(home_recent) / max(len(home_recent), 1),
            sum(away_recent) / max(len(away_recent), 1),
        ])
        
        return features
    
    def update_elo(self, elo_ratings, home_team, away_team, home_goals, away_goals):
        """更新ELO评分"""
        k_factor = 32
        
        # 实际得分
        if home_goals > away_goals:
            actual_home, actual_away = 1.0, 0.0
        elif home_goals < away_goals:
            actual_home, actual_away = 0.0, 1.0
        else:
            actual_home, actual_away = 0.5, 0.5
        
        # 期望得分
        expected_home = 1 / (1 + 10**((elo_ratings[away_team] - elo_ratings[home_team]) / 400))
        expected_away = 1 - expected_home
        
        # 更新评分
        elo_ratings[home_team] += k_factor * (actual_home - expected_home)
        elo_ratings[away_team] += k_factor * (actual_away - expected_away)
    
    def update_stats(self, cumulative_stats, home_team, away_team, home_goals, away_goals):
        """更新累积统计"""
        # 更新主队
        home_stats = cumulative_stats[home_team]
        home_stats['games'] += 1
        home_stats['home_games'] += 1
        home_stats['goals_for'] += home_goals
        home_stats['goals_against'] += away_goals
        
        if home_goals > away_goals:
            home_stats['wins'] += 1
            home_stats['home_wins'] += 1
            home_stats['points'] += 3
            home_stats['recent_form'].append(3)
            if home_goals - away_goals >= 2:
                home_stats['big_wins'] += 1
        elif home_goals == away_goals:
            home_stats['draws'] += 1
            home_stats['home_draws'] += 1
            home_stats['points'] += 1
            home_stats['recent_form'].append(1)
        else:
            home_stats['losses'] += 1
            home_stats['home_losses'] += 1
            home_stats['recent_form'].append(0)
            if away_goals - home_goals >= 2:
                home_stats['big_losses'] += 1
        
        if away_goals == 0:
            home_stats['clean_sheets'] += 1
        if home_goals == 0:
            home_stats['failed_to_score'] += 1
        
        # 更新客队 (类似逻辑)
        away_stats = cumulative_stats[away_team]
        away_stats['games'] += 1
        away_stats['away_games'] += 1
        away_stats['goals_for'] += away_goals
        away_stats['goals_against'] += home_goals
        
        if away_goals > home_goals:
            away_stats['wins'] += 1
            away_stats['away_wins'] += 1
            away_stats['points'] += 3
            away_stats['recent_form'].append(3)
            if away_goals - home_goals >= 2:
                away_stats['big_wins'] += 1
        elif away_goals == home_goals:
            away_stats['draws'] += 1
            away_stats['away_draws'] += 1
            away_stats['points'] += 1
            away_stats['recent_form'].append(1)
        else:
            away_stats['losses'] += 1
            away_stats['away_losses'] += 1
            away_stats['recent_form'].append(0)
            if home_goals - away_goals >= 2:
                away_stats['big_losses'] += 1
        
        if home_goals == 0:
            away_stats['clean_sheets'] += 1
        if away_goals == 0:
            away_stats['failed_to_score'] += 1
    
    def train(self):
        """训练模型"""
        print("🤖 训练XGBoost模型...")
        
        # 准备数据
        matches = self.load_and_prepare_data()
        X, y, final_elo = self.build_features(matches)
        
        print(f"特征维度: {X.shape}")
        print(f"标签分布: 主负{np.sum(y==0)}, 平局{np.sum(y==1)}, 主胜{np.sum(y==2)}")
        
        # 标准化
        X_scaled = self.scaler.fit_transform(X)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 训练
        dtrain = xgb.DMatrix(X_train, label=y_train)
        dtest = xgb.DMatrix(X_test, label=y_test)
        
        self.model = xgb.train(
            self.params,
            dtrain,
            num_boost_round=20,
            evals=[(dtrain, 'train'), (dtest, 'test')],
            early_stopping_rounds=5,
            verbose_eval=False
        )
        
        # 评估
        test_probs = self.model.predict(dtest)
        test_preds = np.argmax(test_probs, axis=1)
        test_acc = accuracy_score(y_test, test_preds)
        
        print(f"测试准确率: {test_acc:.3f}")
        print(f"预测概率范围: [{test_probs.min():.3f}, {test_probs.max():.3f}]")
        
        return final_elo
    
    def predict_match(self, home_team, away_team, current_elo, current_stats):
        """预测单场比赛"""
        features = self.extract_features(home_team, away_team, current_elo, current_stats)
        features_scaled = self.scaler.transform([features])
        
        dtest = xgb.DMatrix(features_scaled)
        probs = self.model.predict(dtest)[0]
        
        return {
            'prob_away_win': probs[0],
            'prob_draw': probs[1],
            'prob_home_win': probs[2],
            'predicted_result': ['客胜', '平局', '主胜'][np.argmax(probs)],
            'confidence': np.max(probs)
        }

def main():
    """主函数"""
    print("🚀 优化的XGBoost足球预测系统")
    print("="*40)
    
    # 创建预测器
    predictor = OptimizedXGBoostPredictor()
    
    # 训练模型
    final_elo = predictor.train()
    
    # 测试几个预测
    print(f"\n🔮 测试预测:")
    
    # 模拟当前统计 (简化)
    current_stats = {}
    teams = ['南通队', '南京队', '常州队']
    for team in teams:
        current_stats[team] = {
            'games': 8, 'wins': 6 if team == '南通队' else 3 if team == '南京队' else 1,
            'draws': 2, 'losses': 0 if team == '南通队' else 3 if team == '南京队' else 5,
            'goals_for': 20 if team == '南通队' else 12 if team == '南京队' else 5,
            'goals_against': 5 if team == '南通队' else 8 if team == '南京队' else 15,
            'points': 20 if team == '南通队' else 11 if team == '南京队' else 5,
            'home_games': 4, 'home_wins': 3 if team == '南通队' else 2 if team == '南京队' else 0,
            'home_draws': 1, 'home_losses': 0 if team == '南通队' else 1 if team == '南京队' else 3,
            'away_games': 4, 'away_wins': 3 if team == '南通队' else 1 if team == '南京队' else 1,
            'away_draws': 1, 'away_losses': 0 if team == '南通队' else 2 if team == '南京队' else 2,
            'recent_form': [3, 3, 3] if team == '南通队' else [1, 3, 0] if team == '南京队' else [0, 0, 1],
            'big_wins': 3 if team == '南通队' else 1 if team == '南京队' else 0,
            'big_losses': 0 if team == '南通队' else 1 if team == '南京队' else 3,
            'clean_sheets': 4 if team == '南通队' else 2 if team == '南京队' else 1,
            'failed_to_score': 0 if team == '南通队' else 1 if team == '南京队' else 3
        }
    
    # 测试预测
    test_matches = [
        ('南通队', '常州队'),
        ('南京队', '常州队'),
        ('南通队', '南京队')
    ]
    
    for home, away in test_matches:
        if home in current_stats and away in current_stats:
            result = predictor.predict_match(home, away, final_elo, current_stats)
            print(f"  {home} vs {away}: {result['predicted_result']} ({result['confidence']:.1%})")
            print(f"    主胜{result['prob_home_win']:.1%}, 平局{result['prob_draw']:.1%}, 客胜{result['prob_away_win']:.1%}")
    
    print(f"\n✅ XGBoost模型训练完成!")
    print(f"💡 使用中等正则化，避免过拟合")
    print(f"🎯 模型能够产生多样化的预测概率")

if __name__ == "__main__":
    main()
