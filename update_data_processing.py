#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于新的正确数据重新生成统计数据
"""

import pandas as pd
import numpy as np
from collections import defaultdict

def load_new_data():
    """加载新的数据文件"""
    print("📊 加载新的正确数据...")
    
    # 读取比赛结果数据
    df_results = pd.read_csv('合并后的比赛数据.csv', encoding='utf-8')
    
    # 读取赛程数据
    df_schedule = pd.read_csv('maches2_utf8.csv', encoding='utf-8')
    
    print(f"比赛结果数据: {df_results.shape}")
    print(f"赛程数据: {df_schedule.shape}")
    
    return df_results, df_schedule

def generate_team_statistics(df_results):
    """生成队伍统计数据"""
    print("🔢 生成队伍统计数据...")
    
    # 获取所有队伍
    teams = set()
    for _, row in df_results.iterrows():
        if pd.notna(row['参赛队伍1']):
            teams.add(row['参赛队伍1'])
        if pd.notna(row['参赛队伍2']):
            teams.add(row['参赛队伍2'])
    
    teams = sorted(list(teams))
    print(f"队伍数量: {len(teams)}")
    print(f"队伍列表: {teams}")
    
    # 初始化统计数据
    team_stats = {}
    for team in teams:
        team_stats[team] = {
            '队伍名称': team,
            '已赛场次': 0, '胜场': 0, '平场': 0, '负场': 0,
            '进球数': 0, '失球数': 0, '净胜球': 0, '积分': 0,
            '胜率': 0.0, '平均进球': 0.0, '平均失球': 0.0,
            '主场胜场': 0, '主场平场': 0, '主场负场': 0,
            '客场胜场': 0, '客场平场': 0, '客场负场': 0,
            '主场进球': 0, '主场失球': 0, '客场进球': 0, '客场失球': 0,
            '最近5场积分': 0, '剩余场次': 0, '理论最高积分': 0, '理论最低积分': 0, 
            '当前排名': 0, '大胜场次': 0, '大败场次': 0, '零封次数': 0, '被零封次数': 0,
            '连胜场次': 0, '连败场次': 0, '不败场次': 0, '主场优势': 0.0,
            '防守稳定性': 0.0, '冲冠希望指数': 0.0, '保级压力指数': 0.0
        }
    
    # 统计比赛结果
    match_records = []  # 记录比赛顺序用于计算连胜等
    
    for _, row in df_results.iterrows():
        if pd.notna(row['参赛队伍1']) and pd.notna(row['参赛队伍2']) and pd.notna(row['参赛队伍1进球数']):
            team1, team2 = row['参赛队伍1'], row['参赛队伍2']
            
            # 处理进球数列名（可能有空格）
            goals1_col = '参赛队伍1进球数' if '参赛队伍1进球数' in row else '参赛队伍1进球 数'
            goals2_col = '参赛队伍2进球数' if '参赛队伍2进球数' in row else '参赛队伍2进球数'
            
            goals1, goals2 = int(row[goals1_col]), int(row[goals2_col])
            is_home = row.get('是否主场', '主场') == '主场'  # 默认team1是主场
            
            # 记录比赛
            match_records.append({
                'round': row['轮次'],
                'team1': team1, 'team2': team2,
                'goals1': goals1, 'goals2': goals2,
                'is_home': is_home
            })
            
            # 基本统计
            team_stats[team1]['已赛场次'] += 1
            team_stats[team2]['已赛场次'] += 1
            team_stats[team1]['进球数'] += goals1
            team_stats[team1]['失球数'] += goals2
            team_stats[team2]['进球数'] += goals2
            team_stats[team2]['失球数'] += goals1
            
            # 主客场统计
            if is_home:
                team_stats[team1]['主场进球'] += goals1
                team_stats[team1]['主场失球'] += goals2
                team_stats[team2]['客场进球'] += goals2
                team_stats[team2]['客场失球'] += goals1
            else:
                team_stats[team1]['客场进球'] += goals1
                team_stats[team1]['客场失球'] += goals2
                team_stats[team2]['主场进球'] += goals2
                team_stats[team2]['主场失球'] += goals1
            
            # 胜负平统计
            if goals1 > goals2:
                # team1胜
                team_stats[team1]['胜场'] += 1
                team_stats[team1]['积分'] += 3
                team_stats[team2]['负场'] += 1
                
                if is_home:
                    team_stats[team1]['主场胜场'] += 1
                    team_stats[team2]['客场负场'] += 1
                else:
                    team_stats[team1]['客场胜场'] += 1
                    team_stats[team2]['主场负场'] += 1
                
                # 大胜统计
                if goals1 - goals2 >= 2:
                    team_stats[team1]['大胜场次'] += 1
                    team_stats[team2]['大败场次'] += 1
                    
            elif goals1 < goals2:
                # team2胜
                team_stats[team2]['胜场'] += 1
                team_stats[team2]['积分'] += 3
                team_stats[team1]['负场'] += 1
                
                if is_home:
                    team_stats[team2]['客场胜场'] += 1
                    team_stats[team1]['主场负场'] += 1
                else:
                    team_stats[team2]['主场胜场'] += 1
                    team_stats[team1]['客场负场'] += 1
                
                # 大胜统计
                if goals2 - goals1 >= 2:
                    team_stats[team2]['大胜场次'] += 1
                    team_stats[team1]['大败场次'] += 1
                    
            else:
                # 平局
                team_stats[team1]['平场'] += 1
                team_stats[team1]['积分'] += 1
                team_stats[team2]['平场'] += 1
                team_stats[team2]['积分'] += 1
                
                if is_home:
                    team_stats[team1]['主场平场'] += 1
                    team_stats[team2]['客场平场'] += 1
                else:
                    team_stats[team1]['客场平场'] += 1
                    team_stats[team2]['主场平场'] += 1
            
            # 零封统计
            if goals1 == 0:
                team_stats[team2]['零封次数'] += 1
                team_stats[team1]['被零封次数'] += 1
            if goals2 == 0:
                team_stats[team1]['零封次数'] += 1
                team_stats[team2]['被零封次数'] += 1
    
    # 计算剩余场次（总共13队，每队应该踢24场）
    total_games_per_team = 24
    for team in teams:
        team_stats[team]['剩余场次'] = total_games_per_team - team_stats[team]['已赛场次']
    
    # 计算衍生指标
    for team in teams:
        stats = team_stats[team]
        if stats['已赛场次'] > 0:
            stats['净胜球'] = stats['进球数'] - stats['失球数']
            stats['胜率'] = round(stats['胜场'] / stats['已赛场次'], 3)
            stats['平均进球'] = round(stats['进球数'] / stats['已赛场次'], 2)
            stats['平均失球'] = round(stats['失球数'] / stats['已赛场次'], 2)
            
            # 主场优势
            home_games = stats['主场胜场'] + stats['主场平场'] + stats['主场负场']
            away_games = stats['客场胜场'] + stats['客场平场'] + stats['客场负场']
            
            if home_games > 0 and away_games > 0:
                home_rate = (stats['主场胜场'] * 3 + stats['主场平场']) / (home_games * 3)
                away_rate = (stats['客场胜场'] * 3 + stats['客场平场']) / (away_games * 3)
                stats['主场优势'] = round(home_rate - away_rate, 3)
        
        stats['理论最高积分'] = stats['积分'] + stats['剩余场次'] * 3
        stats['理论最低积分'] = stats['积分']
    
    # 计算当前排名
    sorted_teams = sorted(team_stats.items(), 
                         key=lambda x: (x[1]['积分'], x[1]['净胜球'], x[1]['进球数']), 
                         reverse=True)
    
    for i, (team, stats) in enumerate(sorted_teams, 1):
        team_stats[team]['当前排名'] = i
    
    return team_stats, match_records

def save_updated_statistics(team_stats):
    """保存更新后的统计数据"""
    print("💾 保存更新后的统计数据...")
    
    # 转换为DataFrame
    df_stats = pd.DataFrame(list(team_stats.values()))
    
    # 按当前排名排序
    df_stats = df_stats.sort_values('当前排名')
    
    # 保存文件
    df_stats.to_csv('team_stats_for_prediction_updated.csv', encoding='utf-8', index=False)
    
    print("✅ 已保存为 team_stats_for_prediction_updated.csv")
    
    # 显示积分榜
    print("\n📊 更新后的积分榜:")
    print("排名  队伍      积分  净胜球  已赛  胜率    剩余场次")
    print("-" * 50)
    
    for _, row in df_stats.iterrows():
        print(f"{row['当前排名']:2d}   {row['队伍名称']:6s}  {row['积分']:2d}   {row['净胜球']:+3d}   {row['已赛场次']:2d}   {row['胜率']:.1%}   {row['剩余场次']:2d}")
    
    return df_stats

def main():
    """主函数"""
    print("🔄 基于新的正确数据重新生成统计")
    print("="*50)
    
    # 加载新数据
    df_results, df_schedule = load_new_data()
    
    # 生成统计数据
    team_stats, match_records = generate_team_statistics(df_results)
    
    # 保存统计数据
    df_stats = save_updated_statistics(team_stats)
    
    print(f"\n✅ 数据更新完成！")
    print(f"📊 处理了 {len(match_records)} 场比赛")
    print(f"🏆 当前领头羊: {df_stats.iloc[0]['队伍名称']} ({df_stats.iloc[0]['积分']}分)")
    print(f"⚠️  当前垫底: {df_stats.iloc[-1]['队伍名称']} ({df_stats.iloc[-1]['积分']}分)")

if __name__ == "__main__":
    main()
